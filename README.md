# CRM Advocacia

Sistema completo de CRM para escritórios de advocacia, com gerenciamento de processos, clientes, documentos e muito mais.

## Funcionalidades

- **Autenticação de usuários**: Sistema de login com controle de acesso por advogado
- **Dashboard**: Visão geral dos processos, prazos e atividades
- **Gerenciamento de Processos**: Cadastro, acompanhamento e controle de processos judiciais
- **Gerenciamento de Clientes**: Cadastro e histórico de clientes
- **Gerenciamento de Documentos**: Upload, geração e controle de documentos
- **Web Scraping**: Captura automática de informações de processos em tribunais
- **Relatórios**: Análises e estatísticas sobre processos e atividades

## Requisitos

- PHP 7.4 ou superior com extensão PDO habilitada
- MySQL 5.7 ou superior
- Navegador web moderno (Chrome, Firefox, Edge)

## Instalação

1. C<PERSON> este repositório ou baixe os arquivos para sua máquina

2. Abra o prompt de comando (CMD) ou PowerShell na pasta do projeto

3. Configure o banco de dados:
   - Verifique as configurações em `database/db_config.php`
   - Ajuste os parâmetros de conexão conforme necessário (host, usuário, senha, etc.)

4. Inicialize o banco de dados:
   ```
   php database/init_db.php
   ```

5. Teste a conexão com o banco de dados:
   ```
   php database/test_connection.php
   ```

6. Inicie o servidor web:
   ```
   start_server.bat
   ```
   Ou manualmente:
   ```
   php -S localhost:8000
   ```

7. Acesse a aplicação no navegador:
   ```
   http://localhost:8000
   ```

## Usuários padrão

Após inicializar o banco de dados, os seguintes usuários estarão disponíveis:

- **Administrador**:
  - Email: <EMAIL>
  - Senha: admin123

- **Advogado**:
  - Email: <EMAIL>
  - Senha: advogado123

## Estrutura do Projeto

```
crm_advocacia/
├── api/                      # API RESTful
│   ├── auth.php              # Endpoints de autenticação
│   ├── clientes.php          # Endpoints de clientes
│   ├── processos.php         # Endpoints de processos
│   ├── documentos.php        # Endpoints de documentos
│   └── .htaccess             # Configuração de roteamento
├── app/                      # Pasta principal da aplicação
│   ├── static/               # Arquivos estáticos (CSS, JS, imagens)
│   │   ├── css/              # Arquivos CSS
│   │   ├── js/               # Arquivos JavaScript
│   │   └── img/              # Imagens
│   ├── templates/            # Templates HTML
│   ├── spiders/              # Web scrapers para captura de dados
│   └── utils/                # Funções utilitárias
├── database/                 # Scripts e configurações do banco de dados
│   ├── schema.sql            # Estrutura do banco de dados
│   ├── seed.sql              # Dados iniciais
│   ├── db_config.php         # Configurações de conexão
│   ├── init_db.php           # Script para inicializar o banco de dados
│   └── test_connection.php   # Script para testar a conexão
├── models/                   # Modelos de dados
│   ├── Usuario.php           # Modelo de usuário
│   ├── Cliente.php           # Modelo de cliente
│   ├── Processo.php          # Modelo de processo
│   └── Documento.php         # Modelo de documento
├── uploads/                  # Pasta para upload de arquivos
├── start_server.bat          # Script para iniciar o servidor web
└── README.md                 # Documentação do projeto
```

## Personalização

O sistema utiliza cores e estilos que remetem a escritórios de advocacia tradicionais:

- **Azul marinho**: Cor principal, transmite seriedade e confiança
- **Dourado**: Cor secundária, transmite sofisticação e tradição
- **Cinza escuro**: Cor de destaque, transmite profissionalismo

Você pode personalizar as cores e estilos editando o arquivo `app/static/css/style.css`.

## Desenvolvimento Futuro

- Integração com mais tribunais para captura automática de processos
- Módulo financeiro para controle de honorários
- Aplicativo mobile para acesso em dispositivos móveis
- Integração com sistemas de assinatura digital
- Módulo de agenda compartilhada entre advogados

## Suporte

Para suporte ou dúvidas, entre em contato através do email: <EMAIL>
