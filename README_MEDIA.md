# Instruções para Arquivos de Mídia

Para que o sistema funcione corretamente com o vídeo de fundo e a logo branca, siga as instruções abaixo:

## Vídeo de Fundo

1. Renomeie seu arquivo de vídeo para `Videodefundo.mp4`
2. Coloque o arquivo na pasta `static/video/`
3. Certifique-se de que o vídeo esteja no formato MP4 e seja otimizado para web (tamanho recomendado: menos de 10MB)

## Logo Branca

1. Renomeie sua imagem de logo para `LOGO BRANCA.png`
2. Coloque o arquivo na pasta `static/img/`
3. Certifique-se de que a logo tenha fundo transparente e seja predominantemente branca para contrastar com o fundo escuro

## Verificação

Após adicionar os arquivos, reinicie o servidor (se necessário) e verifique se:

1. O vídeo de fundo está sendo exibido em todas as páginas
2. A logo branca está sendo exibida no cabeçalho e na página de login

## Solução de Problemas

Se os arquivos não aparecerem:

1. Verifique se os nomes dos arquivos estão exatamente como especificado (incluindo espaços e maiúsculas)
2. Verifique se os arquivos estão nas pastas corretas
3. Limpe o cache do navegador e recarregue a página
4. Reinicie o servidor Flask
