<?php
// Configurações de cabeçalho para API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Incluir modelo de usuário
require_once __DIR__ . '/../models/Usuario.php';

// Iniciar sessão
session_start();

// Obter método HTTP
$method = $_SERVER['REQUEST_METHOD'];

// Obter caminho da URL
$path = isset($_GET['path']) ? $_GET['path'] : '';
$paths = explode('/', $path);
$endpoint = $paths[0] ?? '';

// Instanciar modelo de usuário
$usuarioModel = new Usuario();

// Roteamento da API
switch ($method) {
    case 'POST':
        // Obter dados do corpo da requisição
        $data = json_decode(file_get_contents('php://input'), true);
        
        if ($endpoint === 'login') {
            // Login de usuário
            if (!isset($data['email']) || !isset($data['senha'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Email e senha são obrigatórios']);
                exit;
            }
            
            $usuario = $usuarioModel->verificarCredenciais($data['email'], $data['senha']);
            
            if ($usuario) {
                // Remover senha hash antes de retornar
                unset($usuario['senha_hash']);
                
                // Armazenar usuário na sessão
                $_SESSION['usuario'] = $usuario;
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Login realizado com sucesso',
                    'usuario' => $usuario
                ]);
            } else {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Email ou senha inválidos']);
            }
        } elseif ($endpoint === 'logout') {
            // Logout de usuário
            session_destroy();
            echo json_encode(['success' => true, 'message' => 'Logout realizado com sucesso']);
        } elseif ($endpoint === 'usuarios') {
            // Verificar se o usuário está logado e é administrador
            if (!isset($_SESSION['usuario']) || !$_SESSION['usuario']['admin']) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'Acesso negado']);
                exit;
            }
            
            // Criar novo usuário
            if (!isset($data['nome']) || !isset($data['email']) || !isset($data['senha']) || !isset($data['oab'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
                exit;
            }
            
            try {
                $id = $usuarioModel->criar(
                    $data['nome'],
                    $data['email'],
                    $data['senha'],
                    $data['oab'],
                    $data['cargo'] ?? null,
                    $data['telefone'] ?? null,
                    $data['admin'] ?? false,
                    $data['ativo'] ?? true
                );
                
                echo json_encode([
                    'success' => true,
                    'message' => 'Usuário criado com sucesso',
                    'id' => $id
                ]);
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erro ao criar usuário: ' . $e->getMessage()]);
            }
        } elseif ($endpoint === 'redefinir-senha') {
            // Solicitar redefinição de senha
            if (!isset($data['email'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Email é obrigatório']);
                exit;
            }
            
            $resultado = $usuarioModel->gerarTokenRedefinicaoSenha($data['email']);
            
            if ($resultado) {
                // Aqui você enviaria um email com o link para redefinição
                // Por enquanto, apenas retornamos o token para fins de demonstração
                echo json_encode([
                    'success' => true,
                    'message' => 'Link de redefinição enviado com sucesso',
                    'token' => $resultado['token'],
                    'id' => $resultado['id']
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['success' => false, 'message' => 'Email não encontrado']);
            }
        } elseif ($endpoint === 'confirmar-redefinicao') {
            // Confirmar redefinição de senha
            if (!isset($data['id']) || !isset($data['token']) || !isset($data['senha'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
                exit;
            }
            
            $resultado = $usuarioModel->redefinirSenhaComToken($data['id'], $data['token'], $data['senha']);
            
            if ($resultado) {
                echo json_encode(['success' => true, 'message' => 'Senha redefinida com sucesso']);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Token inválido ou expirado']);
            }
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Endpoint não encontrado']);
        }
        break;
        
    case 'GET':
        if ($endpoint === 'usuarios') {
            // Verificar se o usuário está logado e é administrador
            if (!isset($_SESSION['usuario']) || !$_SESSION['usuario']['admin']) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'Acesso negado']);
                exit;
            }
            
            // Listar usuários
            $usuarios = $usuarioModel->listarTodos();
            
            // Remover senhas hash antes de retornar
            foreach ($usuarios as &$usuario) {
                unset($usuario['senha_hash']);
            }
            
            echo json_encode(['success' => true, 'usuarios' => $usuarios]);
        } elseif ($endpoint === 'usuario-atual') {
            // Obter usuário atual
            if (!isset($_SESSION['usuario'])) {
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => 'Não autenticado']);
                exit;
            }
            
            echo json_encode(['success' => true, 'usuario' => $_SESSION['usuario']]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Endpoint não encontrado']);
        }
        break;
        
    case 'PUT':
        // Obter dados do corpo da requisição
        $data = json_decode(file_get_contents('php://input'), true);
        
        if ($endpoint === 'usuarios' && isset($paths[1])) {
            $id = $paths[1];
            
            // Verificar se o usuário está logado e é administrador ou é o próprio usuário
            if (!isset($_SESSION['usuario']) || (!$_SESSION['usuario']['admin'] && $_SESSION['usuario']['id'] != $id)) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'Acesso negado']);
                exit;
            }
            
            // Atualizar usuário
            if (!isset($data['nome']) || !isset($data['email']) || !isset($data['oab'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
                exit;
            }
            
            try {
                // Se não for admin, não pode alterar o status de admin
                if (!$_SESSION['usuario']['admin']) {
                    $data['admin'] = $_SESSION['usuario']['admin'];
                }
                
                $resultado = $usuarioModel->atualizar(
                    $id,
                    $data['nome'],
                    $data['email'],
                    $data['oab'],
                    $data['cargo'] ?? null,
                    $data['telefone'] ?? null,
                    $data['admin'] ?? false,
                    $data['ativo'] ?? true
                );
                
                if ($resultado) {
                    // Se o usuário atualizou seu próprio perfil, atualizar na sessão
                    if ($_SESSION['usuario']['id'] == $id) {
                        $_SESSION['usuario'] = $usuarioModel->buscarPorId($id);
                        unset($_SESSION['usuario']['senha_hash']);
                    }
                    
                    echo json_encode(['success' => true, 'message' => 'Usuário atualizado com sucesso']);
                } else {
                    http_response_code(500);
                    echo json_encode(['success' => false, 'message' => 'Erro ao atualizar usuário']);
                }
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erro ao atualizar usuário: ' . $e->getMessage()]);
            }
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Endpoint não encontrado']);
        }
        break;
        
    case 'DELETE':
        if ($endpoint === 'usuarios' && isset($paths[1])) {
            $id = $paths[1];
            
            // Verificar se o usuário está logado e é administrador
            if (!isset($_SESSION['usuario']) || !$_SESSION['usuario']['admin']) {
                http_response_code(403);
                echo json_encode(['success' => false, 'message' => 'Acesso negado']);
                exit;
            }
            
            // Não permitir excluir o próprio usuário
            if ($_SESSION['usuario']['id'] == $id) {
                http_response_code(400);
                echo json_encode(['success' => false, 'message' => 'Não é possível excluir o próprio usuário']);
                exit;
            }
            
            try {
                $resultado = $usuarioModel->excluir($id);
                
                if ($resultado) {
                    echo json_encode(['success' => true, 'message' => 'Usuário excluído com sucesso']);
                } else {
                    http_response_code(500);
                    echo json_encode(['success' => false, 'message' => 'Erro ao excluir usuário']);
                }
            } catch (PDOException $e) {
                http_response_code(500);
                echo json_encode(['success' => false, 'message' => 'Erro ao excluir usuário: ' . $e->getMessage()]);
            }
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Endpoint não encontrado']);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'message' => 'Método não permitido']);
        break;
}
