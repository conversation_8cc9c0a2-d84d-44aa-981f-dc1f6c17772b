from flask import Flask, render_template, redirect, url_for, flash, request, jsonify, session, send_from_directory
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta
import os
import secrets
import json
import shutil

# Configuração da aplicação
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///crm_advocacia.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload

# Criar pasta de uploads se não existir
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Inicializar banco de dados
db = SQLAlchemy(app)

# Configurar gerenciador de login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Por favor, faça login para acessar esta página.'
login_manager.login_message_category = 'info'

# Modelos de dados
class Usuario(db.Model, UserMixin):
    id = db.Column(db.Integer, primary_key=True)
    nome = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    senha_hash = db.Column(db.String(128), nullable=False)
    oab = db.Column(db.String(20), unique=True)
    cargo = db.Column(db.String(50))
    telefone = db.Column(db.String(20))
    data_criacao = db.Column(db.DateTime, default=datetime.utcnow)
    ultimo_acesso = db.Column(db.DateTime)
    ativo = db.Column(db.Boolean, default=True)
    admin = db.Column(db.Boolean, default=False)
    token_redefinicao = db.Column(db.String(100))
    expiracao_token = db.Column(db.DateTime)

    def set_senha(self, senha):
        self.senha_hash = generate_password_hash(senha)

    def verificar_senha(self, senha):
        return check_password_hash(self.senha_hash, senha)

    def get_id(self):
        return str(self.id)

class Cliente(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nome = db.Column(db.String(100), nullable=False)
    cpf_cnpj = db.Column(db.String(20), unique=True, nullable=False)
    tipo = db.Column(db.String(2), nullable=False)  # PF ou PJ
    email = db.Column(db.String(100))
    telefone = db.Column(db.String(20))
    celular = db.Column(db.String(20))
    endereco = db.Column(db.String(255))
    cidade = db.Column(db.String(100))
    estado = db.Column(db.String(2))
    cep = db.Column(db.String(10))
    data_nascimento = db.Column(db.Date)
    observacoes = db.Column(db.Text)
    data_cadastro = db.Column(db.DateTime, default=datetime.utcnow)
    ativo = db.Column(db.Boolean, default=True)
    advogado_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)

    advogado = db.relationship('Usuario', backref=db.backref('clientes', lazy=True))

class Processo(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    numero = db.Column(db.String(50), unique=True, nullable=False)
    titulo = db.Column(db.String(255), nullable=False)
    descricao = db.Column(db.Text)
    tipo = db.Column(db.String(50))
    area = db.Column(db.String(50))
    vara = db.Column(db.String(100))
    comarca = db.Column(db.String(100))
    tribunal = db.Column(db.String(100))
    valor_causa = db.Column(db.Float)
    status = db.Column(db.String(50))
    data_inicio = db.Column(db.Date)
    data_conclusao = db.Column(db.Date)
    data_cadastro = db.Column(db.DateTime, default=datetime.utcnow)
    cliente_id = db.Column(db.Integer, db.ForeignKey('cliente.id'), nullable=False)
    advogado_id = db.Column(db.Integer, db.ForeignKey('usuario.id'), nullable=False)

    cliente = db.relationship('Cliente', backref=db.backref('processos', lazy=True))
    advogado = db.relationship('Usuario', backref=db.backref('processos', lazy=True))

class Andamento(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    data = db.Column(db.DateTime, nullable=False)
    descricao = db.Column(db.Text, nullable=False)
    tipo = db.Column(db.String(50))
    observacoes = db.Column(db.Text)
    processo_id = db.Column(db.Integer, db.ForeignKey('processo.id', ondelete='CASCADE'), nullable=False)

    processo = db.relationship('Processo', backref=db.backref('andamentos', lazy=True, cascade='all, delete-orphan'))

class Prazo(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    titulo = db.Column(db.String(255), nullable=False)
    descricao = db.Column(db.Text)
    data_inicio = db.Column(db.DateTime, nullable=False)
    data_fim = db.Column(db.DateTime, nullable=False)
    concluido = db.Column(db.Boolean, default=False)
    prioridade = db.Column(db.String(20))
    processo_id = db.Column(db.Integer, db.ForeignKey('processo.id', ondelete='CASCADE'), nullable=False)

    processo = db.relationship('Processo', backref=db.backref('prazos', lazy=True, cascade='all, delete-orphan'))

class Documento(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    nome = db.Column(db.String(255), nullable=False)
    tipo = db.Column(db.String(50))
    descricao = db.Column(db.Text)
    caminho_arquivo = db.Column(db.String(255), nullable=False)
    data_upload = db.Column(db.DateTime, default=datetime.utcnow)
    tamanho = db.Column(db.Integer)
    formato = db.Column(db.String(10))
    processo_id = db.Column(db.Integer, db.ForeignKey('processo.id', ondelete='CASCADE'))
    cliente_id = db.Column(db.Integer, db.ForeignKey('cliente.id', ondelete='CASCADE'))

    processo = db.relationship('Processo', backref=db.backref('documentos', lazy=True, cascade='all, delete-orphan'))
    cliente = db.relationship('Cliente', backref=db.backref('documentos', lazy=True, cascade='all, delete-orphan'))

@login_manager.user_loader
def load_user(user_id):
    return Usuario.query.get(int(user_id))

# Rota para servir arquivos estáticos
@app.route('/static/<path:filename>')
def serve_static(filename):
    return send_from_directory('static', filename)

# Rotas
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        email = request.form.get('email')
        senha = request.form.get('senha')
        lembrar = 'lembrar_me' in request.form

        usuario = Usuario.query.filter_by(email=email).first()

        if usuario and usuario.verificar_senha(senha) and usuario.ativo:
            login_user(usuario, remember=lembrar)
            usuario.ultimo_acesso = datetime.utcnow()
            db.session.commit()

            # Armazenar flag para mostrar mensagem de boas-vindas
            session['mostrar_boas_vindas'] = True

            next_page = request.args.get('next')
            return redirect(next_page or url_for('dashboard'))
        else:
            flash('Email ou senha inválidos', 'danger')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('Você foi desconectado com sucesso', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Contar processos por status
    processos_por_status = db.session.query(
        Processo.status, db.func.count(Processo.id)
    ).filter_by(advogado_id=current_user.id).group_by(Processo.status).all()

    # Obter processos recentes
    processos_recentes = Processo.query.filter_by(
        advogado_id=current_user.id
    ).order_by(Processo.data_cadastro.desc()).limit(5).all()

    # Obter prazos próximos
    data_limite = datetime.now() + timedelta(days=7)
    prazos_proximos = Prazo.query.join(Processo).filter(
        Processo.advogado_id == current_user.id,
        Prazo.concluido == False,
        Prazo.data_fim <= data_limite
    ).order_by(Prazo.data_fim).all()

    # Contar totais
    total_processos = Processo.query.filter_by(advogado_id=current_user.id).count()
    total_clientes = Cliente.query.filter_by(advogado_id=current_user.id).count()

    # Verificar se deve mostrar mensagem de boas-vindas
    mostrar_boas_vindas = session.pop('mostrar_boas_vindas', False)

    return render_template(
        'dashboard.html',
        processos_por_status=processos_por_status,
        processos_recentes=processos_recentes,
        prazos_proximos=prazos_proximos,
        total_processos=total_processos,
        total_clientes=total_clientes,
        mostrar_boas_vindas=mostrar_boas_vindas
    )

@app.route('/usuarios')
@login_required
def listar_usuarios():
    if not current_user.admin:
        flash('Você não tem permissão para acessar esta página', 'danger')
        return redirect(url_for('dashboard'))

    usuarios = Usuario.query.all()
    return render_template('usuarios/listar.html', usuarios=usuarios)

@app.route('/usuarios/novo', methods=['GET', 'POST'])
@login_required
def novo_usuario():
    if not current_user.admin:
        flash('Você não tem permissão para acessar esta página', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        nome = request.form.get('nome')
        email = request.form.get('email')
        oab = request.form.get('oab')
        cargo = request.form.get('cargo')
        telefone = request.form.get('telefone')
        senha = request.form.get('senha')
        admin = 'admin' in request.form
        ativo = 'ativo' in request.form

        # Verificar se email já existe
        if Usuario.query.filter_by(email=email).first():
            flash('Este email já está em uso', 'danger')
            return redirect(url_for('novo_usuario'))

        # Verificar se OAB já existe
        if oab and Usuario.query.filter_by(oab=oab).first():
            flash('Este número de OAB já está registrado', 'danger')
            return redirect(url_for('novo_usuario'))

        usuario = Usuario(
            nome=nome,
            email=email,
            oab=oab,
            cargo=cargo,
            telefone=telefone,
            admin=admin,
            ativo=ativo
        )
        usuario.set_senha(senha)

        db.session.add(usuario)
        db.session.commit()

        flash('Usuário criado com sucesso', 'success')
        return redirect(url_for('listar_usuarios'))

    return render_template('usuarios/novo.html')

@app.route('/redefinir-senha/<token>')
def redefinir_senha(token):
    return render_template('redefinir_senha.html', token=token)

# Inicialização do banco de dados
@app.cli.command('init-db')
def init_db_command():
    """Inicializa o banco de dados."""
    db.create_all()

    # Verificar se já existem usuários
    if Usuario.query.count() == 0:
        # Criar usuário administrador
        admin = Usuario(
            nome="Administrador",
            email="<EMAIL>",
            oab="123456",
            cargo="Administrador",
            telefone="(11) 99999-9999",
            admin=True,
            ativo=True
        )
        admin.set_senha("admin123")

        # Criar usuário advogado
        advogado = Usuario(
            nome="João Advogado",
            email="<EMAIL>",
            oab="654321",
            cargo="Advogado",
            telefone="(11) 88888-8888",
            admin=False,
            ativo=True
        )
        advogado.set_senha("advogado123")

        db.session.add(admin)
        db.session.add(advogado)
        db.session.commit()

        print("Usuários criados com sucesso!")
        print("Admin: <EMAIL> / admin123")
        print("Advogado: <EMAIL> / advogado123")

# Executar a aplicação
if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # Verificar se já existem usuários
        if Usuario.query.count() == 0:
            # Criar usuário administrador
            admin = Usuario(
                nome="Administrador",
                email="<EMAIL>",
                oab="123456",
                cargo="Administrador",
                telefone="(11) 99999-9999",
                admin=True,
                ativo=True
            )
            admin.set_senha("admin123")

            # Criar usuário advogado
            advogado = Usuario(
                nome="João Advogado",
                email="<EMAIL>",
                oab="654321",
                cargo="Advogado",
                telefone="(11) 88888-8888",
                admin=False,
                ativo=True
            )
            advogado.set_senha("advogado123")

            db.session.add(admin)
            db.session.add(advogado)
            db.session.commit()

            print("Usuários criados com sucesso!")
            print("Admin: <EMAIL> / admin123")
            print("Advogado: <EMAIL> / advogado123")

    app.run(debug=True)
