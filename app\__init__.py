from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager
from config import config

# Inicialização das extensões
db = SQLAlchemy()
login_manager = LoginManager()
login_manager.login_view = 'auth.login'
login_manager.login_message = 'Por favor, faça login para acessar esta página.'
login_manager.login_message_category = 'info'

def create_app(config_name):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Inicializar extensões com a aplicação
    db.init_app(app)
    login_manager.init_app(app)
    
    # Criar pasta de uploads se não existir
    import os
    if not os.path.exists(app.config['UPLOAD_FOLDER']):
        os.makedirs(app.config['UPLOAD_FOLDER'])
    
    # Registrar blueprints
    from app.controllers.auth import auth as auth_blueprint
    app.register_blueprint(auth_blueprint)
    
    from app.controllers.main import main as main_blueprint
    app.register_blueprint(main_blueprint)
    
    from app.controllers.processos import processos as processos_blueprint
    app.register_blueprint(processos_blueprint)
    
    from app.controllers.clientes import clientes as clientes_blueprint
    app.register_blueprint(clientes_blueprint)
    
    from app.controllers.documentos import documentos as documentos_blueprint
    app.register_blueprint(documentos_blueprint)
    
    # Configurar manipuladores de erro
    from app.controllers.errors import errors as errors_blueprint
    app.register_blueprint(errors_blueprint)
    
    return app
