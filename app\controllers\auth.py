from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.urls import url_parse
from app import db
from app.models.usuario import Usuario
from app.forms.auth import LoginForm, RegistroForm

auth = Blueprint('auth', __name__)

@auth.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        usuario = Usuario.query.filter_by(email=form.email.data).first()
        if usuario is None or not usuario.verificar_senha(form.senha.data):
            flash('Email ou senha inválidos', 'danger')
            return redirect(url_for('auth.login'))
        
        login_user(usuario, remember=form.lembrar_me.data)
        
        # Atualizar último acesso
        from datetime import datetime
        usuario.ultimo_acesso = datetime.utcnow()
        db.session.commit()
        
        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('main.dashboard')
        
        flash('Login realizado com sucesso!', 'success')
        return redirect(next_page)
    
    return render_template('auth/login.html', title='Login', form=form)

@auth.route('/logout')
@login_required
def logout():
    logout_user()
    flash('Você foi desconectado com sucesso.', 'info')
    return redirect(url_for('auth.login'))

@auth.route('/registro', methods=['GET', 'POST'])
def registro():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = RegistroForm()
    if form.validate_on_submit():
        usuario = Usuario(
            nome=form.nome.data,
            email=form.email.data,
            oab=form.oab.data,
            cargo=form.cargo.data,
            telefone=form.telefone.data
        )
        usuario.senha = form.senha.data
        
        # O primeiro usuário registrado será administrador
        if Usuario.query.count() == 0:
            usuario.admin = True
        
        db.session.add(usuario)
        db.session.commit()
        
        flash('Registro realizado com sucesso! Agora você pode fazer login.', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/registro.html', title='Registro', form=form)

@auth.route('/perfil', methods=['GET', 'POST'])
@login_required
def perfil():
    from app.forms.auth import PerfilForm
    
    form = PerfilForm()
    if form.validate_on_submit():
        current_user.nome = form.nome.data
        current_user.telefone = form.telefone.data
        
        if form.senha.data:
            current_user.senha = form.senha.data
        
        db.session.commit()
        flash('Seu perfil foi atualizado com sucesso!', 'success')
        return redirect(url_for('auth.perfil'))
    elif request.method == 'GET':
        form.nome.data = current_user.nome
        form.email.data = current_user.email
        form.oab.data = current_user.oab
        form.cargo.data = current_user.cargo
        form.telefone.data = current_user.telefone
    
    return render_template('auth/perfil.html', title='Perfil', form=form)
