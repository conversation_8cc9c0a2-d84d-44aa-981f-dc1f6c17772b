from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.urls import url_parse
from app import db
from app.models.usuario import Usuario
from app.forms.auth import LoginForm, RegistroForm, PerfilForm
from datetime import datetime
import json

auth_controller = Blueprint('auth_controller', __name__)

@auth_controller.route('/login', methods=['GET', 'POST'])
def login():
    """
    Rota para login de usuários.
    GET: Exibe o formulário de login
    POST: Processa o login
    """
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        usuario = Usuario.query.filter_by(email=form.email.data).first()
        if usuario is None or not usuario.verificar_senha(form.senha.data):
            flash('Email ou senha inválidos', 'danger')
            return redirect(url_for('auth_controller.login'))
        
        # Verificar se o usuário está ativo
        if not usuario.ativo:
            flash('Sua conta está desativada. Entre em contato com o administrador.', 'warning')
            return redirect(url_for('auth_controller.login'))
        
        login_user(usuario, remember=form.lembrar_me.data)
        
        # Atualizar último acesso
        usuario.ultimo_acesso = datetime.utcnow()
        db.session.commit()
        
        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('main.dashboard')
        
        flash('Login realizado com sucesso!', 'success')
        return redirect(next_page)
    
    return render_template('auth/login.html', title='Login', form=form)

@auth_controller.route('/logout')
@login_required
def logout():
    """
    Rota para logout de usuários.
    """
    logout_user()
    flash('Você foi desconectado com sucesso.', 'info')
    return redirect(url_for('auth_controller.login'))

@auth_controller.route('/perfil', methods=['GET', 'POST'])
@login_required
def perfil():
    """
    Rota para visualização e edição do perfil do usuário.
    GET: Exibe o formulário de perfil
    POST: Processa a atualização do perfil
    """
    form = PerfilForm()
    if form.validate_on_submit():
        current_user.nome = form.nome.data
        current_user.telefone = form.telefone.data
        
        if form.senha.data:
            current_user.senha = form.senha.data
        
        db.session.commit()
        flash('Seu perfil foi atualizado com sucesso!', 'success')
        return redirect(url_for('auth_controller.perfil'))
    elif request.method == 'GET':
        form.nome.data = current_user.nome
        form.email.data = current_user.email
        form.oab.data = current_user.oab
        form.cargo.data = current_user.cargo
        form.telefone.data = current_user.telefone
    
    return render_template('auth/perfil.html', title='Perfil', form=form)

@auth_controller.route('/usuarios', methods=['GET'])
@login_required
def listar_usuarios():
    """
    Rota para listar usuários (apenas para administradores).
    """
    if not current_user.admin:
        flash('Você não tem permissão para acessar esta página.', 'danger')
        return redirect(url_for('main.dashboard'))
    
    usuarios = Usuario.query.all()
    return render_template('auth/usuarios.html', title='Usuários', usuarios=usuarios)

@auth_controller.route('/usuarios/novo', methods=['POST'])
@login_required
def novo_usuario():
    """
    Rota para criar um novo usuário (apenas para administradores).
    """
    if not current_user.admin:
        return jsonify({'success': False, 'message': 'Você não tem permissão para criar usuários.'}), 403
    
    try:
        data = request.json
        
        # Verificar se o email já existe
        if Usuario.query.filter_by(email=data['email']).first():
            return jsonify({'success': False, 'message': 'Este email já está em uso.'}), 400
        
        # Verificar se o OAB já existe
        if Usuario.query.filter_by(oab=data['oab']).first():
            return jsonify({'success': False, 'message': 'Este número de OAB já está registrado.'}), 400
        
        # Criar novo usuário
        usuario = Usuario(
            nome=data['nome'],
            email=data['email'],
            oab=data['oab'],
            cargo=data['cargo'],
            telefone=data['telefone'],
            admin=data['tipo'] == 'admin',
            ativo=data.get('ativo', True)
        )
        usuario.senha = data['senha']
        
        db.session.add(usuario)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Usuário criado com sucesso!'}), 201
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Erro ao criar usuário: {str(e)}'}), 500

@auth_controller.route('/usuarios/<int:id>', methods=['PUT'])
@login_required
def atualizar_usuario(id):
    """
    Rota para atualizar um usuário (apenas para administradores).
    """
    if not current_user.admin:
        return jsonify({'success': False, 'message': 'Você não tem permissão para editar usuários.'}), 403
    
    try:
        usuario = Usuario.query.get_or_404(id)
        data = request.json
        
        # Verificar se o email já existe e não é do usuário atual
        email_existente = Usuario.query.filter_by(email=data['email']).first()
        if email_existente and email_existente.id != id:
            return jsonify({'success': False, 'message': 'Este email já está em uso.'}), 400
        
        # Verificar se o OAB já existe e não é do usuário atual
        oab_existente = Usuario.query.filter_by(oab=data['oab']).first()
        if oab_existente and oab_existente.id != id:
            return jsonify({'success': False, 'message': 'Este número de OAB já está registrado.'}), 400
        
        # Atualizar usuário
        usuario.nome = data['nome']
        usuario.email = data['email']
        usuario.oab = data['oab']
        usuario.cargo = data['cargo']
        usuario.telefone = data['telefone']
        usuario.admin = data['tipo'] == 'admin'
        usuario.ativo = data.get('ativo', True)
        
        if data.get('senha'):
            usuario.senha = data['senha']
        
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Usuário atualizado com sucesso!'}), 200
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Erro ao atualizar usuário: {str(e)}'}), 500

@auth_controller.route('/usuarios/<int:id>', methods=['DELETE'])
@login_required
def excluir_usuario(id):
    """
    Rota para excluir um usuário (apenas para administradores).
    """
    if not current_user.admin:
        return jsonify({'success': False, 'message': 'Você não tem permissão para excluir usuários.'}), 403
    
    try:
        usuario = Usuario.query.get_or_404(id)
        
        # Não permitir excluir o próprio usuário
        if usuario.id == current_user.id:
            return jsonify({'success': False, 'message': 'Você não pode excluir seu próprio usuário.'}), 400
        
        db.session.delete(usuario)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Usuário excluído com sucesso!'}), 200
    
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'Erro ao excluir usuário: {str(e)}'}), 500
