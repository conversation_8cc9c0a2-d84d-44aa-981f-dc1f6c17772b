from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.models.cliente import Cliente
from app.models.processo import Processo
from app.forms.cliente import ClienteForm

clientes = Blueprint('clientes', __name__)

@clientes.route('/clientes')
@login_required
def listar():
    page = request.args.get('page', 1, type=int)
    filtro = request.args.get('filtro', '')
    
    query = Cliente.query.filter_by(advogado_id=current_user.id)
    
    if filtro:
        query = query.filter(
            (Cliente.nome.contains(filtro)) |
            (Cliente.cpf_cnpj.contains(filtro)) |
            (Cliente.email.contains(filtro))
        )
    
    clientes = query.order_by(Cliente.nome).paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template(
        'clientes/listar.html',
        title='Clientes',
        clientes=clientes,
        filtro=filtro
    )

@clientes.route('/clientes/novo', methods=['GET', 'POST'])
@login_required
def novo():
    form = ClienteForm()
    
    if form.validate_on_submit():
        cliente = Cliente(
            nome=form.nome.data,
            cpf_cnpj=form.cpf_cnpj.data,
            tipo=form.tipo.data,
            email=form.email.data,
            telefone=form.telefone.data,
            celular=form.celular.data,
            endereco=form.endereco.data,
            cidade=form.cidade.data,
            estado=form.estado.data,
            cep=form.cep.data,
            data_nascimento=form.data_nascimento.data,
            observacoes=form.observacoes.data,
            advogado_id=current_user.id
        )
        
        db.session.add(cliente)
        db.session.commit()
        
        flash('Cliente cadastrado com sucesso!', 'success')
        return redirect(url_for('clientes.visualizar', id=cliente.id))
    
    return render_template(
        'clientes/form.html',
        title='Novo Cliente',
        form=form
    )

@clientes.route('/clientes/<int:id>')
@login_required
def visualizar(id):
    cliente = Cliente.query.get_or_404(id)
    
    # Verificar se o cliente pertence ao advogado logado
    if cliente.advogado_id != current_user.id and not current_user.admin:
        flash('Você não tem permissão para acessar este cliente.', 'danger')
        return redirect(url_for('clientes.listar'))
    
    # Obter processos do cliente
    processos = Processo.query.filter_by(cliente_id=id).all()
    
    return render_template(
        'clientes/visualizar.html',
        title=f'Cliente {cliente.nome}',
        cliente=cliente,
        processos=processos
    )

@clientes.route('/clientes/<int:id>/editar', methods=['GET', 'POST'])
@login_required
def editar(id):
    cliente = Cliente.query.get_or_404(id)
    
    # Verificar se o cliente pertence ao advogado logado
    if cliente.advogado_id != current_user.id and not current_user.admin:
        flash('Você não tem permissão para editar este cliente.', 'danger')
        return redirect(url_for('clientes.listar'))
    
    form = ClienteForm()
    
    if form.validate_on_submit():
        cliente.nome = form.nome.data
        cliente.cpf_cnpj = form.cpf_cnpj.data
        cliente.tipo = form.tipo.data
        cliente.email = form.email.data
        cliente.telefone = form.telefone.data
        cliente.celular = form.celular.data
        cliente.endereco = form.endereco.data
        cliente.cidade = form.cidade.data
        cliente.estado = form.estado.data
        cliente.cep = form.cep.data
        cliente.data_nascimento = form.data_nascimento.data
        cliente.observacoes = form.observacoes.data
        
        db.session.commit()
        
        flash('Cliente atualizado com sucesso!', 'success')
        return redirect(url_for('clientes.visualizar', id=cliente.id))
    elif request.method == 'GET':
        form.nome.data = cliente.nome
        form.cpf_cnpj.data = cliente.cpf_cnpj
        form.tipo.data = cliente.tipo
        form.email.data = cliente.email
        form.telefone.data = cliente.telefone
        form.celular.data = cliente.celular
        form.endereco.data = cliente.endereco
        form.cidade.data = cliente.cidade
        form.estado.data = cliente.estado
        form.cep.data = cliente.cep
        form.data_nascimento.data = cliente.data_nascimento
        form.observacoes.data = cliente.observacoes
    
    return render_template(
        'clientes/form.html',
        title=f'Editar Cliente {cliente.nome}',
        form=form,
        cliente=cliente
    )
