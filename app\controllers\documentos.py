import os
from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, send_from_directory
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app import db
from app.models.documento import Documento, ModeloDocumento
from app.models.processo import Processo
from app.models.cliente import Cliente
from app.forms.documento import DocumentoForm, ModeloDocumentoForm, GerarDocumentoForm
from app.utils.documento import gerar_documento_a_partir_de_modelo

documentos = Blueprint('documentos', __name__)

def extensao_permitida(filename):
    EXTENSOES_PERMITIDAS = {'pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in EXTENSOES_PERMITIDAS

@documentos.route('/documentos')
@login_required
def listar():
    page = request.args.get('page', 1, type=int)
    filtro = request.args.get('filtro', '')
    
    # Obter documentos dos processos do advogado
    query = Documento.query.join(
        Processo, Documento.processo_id == Processo.id
    ).filter(
        Processo.advogado_id == current_user.id
    )
    
    if filtro:
        query = query.filter(
            (Documento.nome.contains(filtro)) |
            (Documento.tipo.contains(filtro))
        )
    
    documentos = query.order_by(Documento.data_upload.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template(
        'documentos/listar.html',
        title='Documentos',
        documentos=documentos,
        filtro=filtro
    )

@documentos.route('/documentos/novo', methods=['GET', 'POST'])
@login_required
def novo():
    form = DocumentoForm()
    
    # Carregar processos do advogado para o select
    form.processo_id.choices = [
        (p.id, f"{p.numero} - {p.titulo}") 
        for p in Processo.query.filter_by(advogado_id=current_user.id).all()
    ]
    
    # Carregar clientes do advogado para o select
    form.cliente_id.choices = [
        (c.id, c.nome) 
        for c in Cliente.query.filter_by(advogado_id=current_user.id).all()
    ]
    
    if form.validate_on_submit():
        arquivo = form.arquivo.data
        if arquivo and extensao_permitida(arquivo.filename):
            filename = secure_filename(arquivo.filename)
            
            # Criar diretório para o arquivo se não existir
            upload_folder = current_app.config['UPLOAD_FOLDER']
            if not os.path.exists(upload_folder):
                os.makedirs(upload_folder)
            
            # Salvar arquivo
            filepath = os.path.join(upload_folder, filename)
            arquivo.save(filepath)
            
            # Obter tamanho e formato do arquivo
            tamanho = os.path.getsize(filepath)
            formato = filename.rsplit('.', 1)[1].lower()
            
            # Criar registro no banco de dados
            documento = Documento(
                nome=form.nome.data,
                tipo=form.tipo.data,
                descricao=form.descricao.data,
                caminho_arquivo=filename,
                tamanho=tamanho,
                formato=formato,
                processo_id=form.processo_id.data if form.processo_id.data else None,
                cliente_id=form.cliente_id.data if form.cliente_id.data else None
            )
            
            db.session.add(documento)
            db.session.commit()
            
            flash('Documento cadastrado com sucesso!', 'success')
            return redirect(url_for('documentos.listar'))
        else:
            flash('Formato de arquivo não permitido.', 'danger')
    
    return render_template(
        'documentos/form.html',
        title='Novo Documento',
        form=form
    )

@documentos.route('/documentos/<int:id>')
@login_required
def visualizar(id):
    documento = Documento.query.get_or_404(id)
    
    # Verificar se o documento pertence a um processo do advogado logado
    if documento.processo_id:
        processo = Processo.query.get(documento.processo_id)
        if processo.advogado_id != current_user.id and not current_user.admin:
            flash('Você não tem permissão para acessar este documento.', 'danger')
            return redirect(url_for('documentos.listar'))
    
    return render_template(
        'documentos/visualizar.html',
        title=f'Documento {documento.nome}',
        documento=documento
    )

@documentos.route('/documentos/download/<int:id>')
@login_required
def download(id):
    documento = Documento.query.get_or_404(id)
    
    # Verificar se o documento pertence a um processo do advogado logado
    if documento.processo_id:
        processo = Processo.query.get(documento.processo_id)
        if processo.advogado_id != current_user.id and not current_user.admin:
            flash('Você não tem permissão para baixar este documento.', 'danger')
            return redirect(url_for('documentos.listar'))
    
    return send_from_directory(
        current_app.config['UPLOAD_FOLDER'],
        documento.caminho_arquivo,
        as_attachment=True
    )
