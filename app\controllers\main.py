from flask import Blueprint, render_template
from flask_login import login_required, current_user
from app.models.processo import Processo, Prazo
from app.models.cliente import Cliente
from datetime import datetime, timedelta
from sqlalchemy import func

main = Blueprint('main', __name__)

@main.route('/')
def index():
    return render_template('index.html', title='Início')

@main.route('/dashboard')
@login_required
def dashboard():
    # Obter estatísticas para o dashboard
    
    # Total de processos do advogado
    total_processos = Processo.query.filter_by(advogado_id=current_user.id).count()
    
    # Total de clientes do advogado
    total_clientes = Cliente.query.filter_by(advogado_id=current_user.id).count()
    
    # Processos por status
    processos_por_status = db.session.query(
        Processo.status, func.count(Processo.id)
    ).filter_by(
        advogado_id=current_user.id
    ).group_by(
        Processo.status
    ).all()
    
    # Prazos próximos (próximos 7 dias)
    hoje = datetime.now().date()
    data_limite = hoje + timedelta(days=7)
    
    prazos_proximos = Prazo.query.join(
        Processo, Prazo.processo_id == Processo.id
    ).filter(
        Processo.advogado_id == current_user.id,
        Prazo.data_fim >= hoje,
        Prazo.data_fim <= data_limite,
        Prazo.concluido == False
    ).order_by(
        Prazo.data_fim
    ).limit(5).all()
    
    # Processos recentes
    processos_recentes = Processo.query.filter_by(
        advogado_id=current_user.id
    ).order_by(
        Processo.data_cadastro.desc()
    ).limit(5).all()
    
    return render_template(
        'dashboard/index.html',
        title='Dashboard',
        total_processos=total_processos,
        total_clientes=total_clientes,
        processos_por_status=processos_por_status,
        prazos_proximos=prazos_proximos,
        processos_recentes=processos_recentes
    )
