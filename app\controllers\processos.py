from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.models.processo import Processo, Andamento, Prazo
from app.models.cliente import Cliente
from app.forms.processo import ProcessoForm, AndamentoForm, PrazoForm

processos = Blueprint('processos', __name__)

@processos.route('/processos')
@login_required
def listar():
    page = request.args.get('page', 1, type=int)
    filtro = request.args.get('filtro', '')
    
    query = Processo.query.filter_by(advogado_id=current_user.id)
    
    if filtro:
        query = query.filter(
            (Processo.numero.contains(filtro)) |
            (Processo.titulo.contains(filtro)) |
            (Processo.status.contains(filtro))
        )
    
    processos = query.order_by(Processo.data_cadastro.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    
    return render_template(
        'processos/listar.html',
        title='Processos',
        processos=processos,
        filtro=filtro
    )

@processos.route('/processos/novo', methods=['GET', 'POST'])
@login_required
def novo():
    form = ProcessoForm()
    
    # Carregar clientes do advogado para o select
    form.cliente_id.choices = [
        (c.id, c.nome) for c in Cliente.query.filter_by(advogado_id=current_user.id).all()
    ]
    
    if form.validate_on_submit():
        processo = Processo(
            numero=form.numero.data,
            titulo=form.titulo.data,
            descricao=form.descricao.data,
            tipo=form.tipo.data,
            area=form.area.data,
            vara=form.vara.data,
            comarca=form.comarca.data,
            tribunal=form.tribunal.data,
            valor_causa=form.valor_causa.data,
            status=form.status.data,
            data_inicio=form.data_inicio.data,
            cliente_id=form.cliente_id.data,
            advogado_id=current_user.id
        )
        
        db.session.add(processo)
        db.session.commit()
        
        flash('Processo cadastrado com sucesso!', 'success')
        return redirect(url_for('processos.visualizar', id=processo.id))
    
    return render_template(
        'processos/form.html',
        title='Novo Processo',
        form=form
    )

@processos.route('/processos/<int:id>')
@login_required
def visualizar(id):
    processo = Processo.query.get_or_404(id)
    
    # Verificar se o processo pertence ao advogado logado
    if processo.advogado_id != current_user.id and not current_user.admin:
        flash('Você não tem permissão para acessar este processo.', 'danger')
        return redirect(url_for('processos.listar'))
    
    andamentos = Andamento.query.filter_by(processo_id=id).order_by(Andamento.data.desc()).all()
    prazos = Prazo.query.filter_by(processo_id=id).order_by(Prazo.data_fim).all()
    
    return render_template(
        'processos/visualizar.html',
        title=f'Processo {processo.numero}',
        processo=processo,
        andamentos=andamentos,
        prazos=prazos
    )

@processos.route('/processos/<int:id>/editar', methods=['GET', 'POST'])
@login_required
def editar(id):
    processo = Processo.query.get_or_404(id)
    
    # Verificar se o processo pertence ao advogado logado
    if processo.advogado_id != current_user.id and not current_user.admin:
        flash('Você não tem permissão para editar este processo.', 'danger')
        return redirect(url_for('processos.listar'))
    
    form = ProcessoForm()
    
    # Carregar clientes do advogado para o select
    form.cliente_id.choices = [
        (c.id, c.nome) for c in Cliente.query.filter_by(advogado_id=current_user.id).all()
    ]
    
    if form.validate_on_submit():
        processo.numero = form.numero.data
        processo.titulo = form.titulo.data
        processo.descricao = form.descricao.data
        processo.tipo = form.tipo.data
        processo.area = form.area.data
        processo.vara = form.vara.data
        processo.comarca = form.comarca.data
        processo.tribunal = form.tribunal.data
        processo.valor_causa = form.valor_causa.data
        processo.status = form.status.data
        processo.data_inicio = form.data_inicio.data
        processo.cliente_id = form.cliente_id.data
        
        db.session.commit()
        
        flash('Processo atualizado com sucesso!', 'success')
        return redirect(url_for('processos.visualizar', id=processo.id))
    elif request.method == 'GET':
        form.numero.data = processo.numero
        form.titulo.data = processo.titulo
        form.descricao.data = processo.descricao
        form.tipo.data = processo.tipo
        form.area.data = processo.area
        form.vara.data = processo.vara
        form.comarca.data = processo.comarca
        form.tribunal.data = processo.tribunal
        form.valor_causa.data = processo.valor_causa
        form.status.data = processo.status
        form.data_inicio.data = processo.data_inicio
        form.cliente_id.data = processo.cliente_id
    
    return render_template(
        'processos/form.html',
        title=f'Editar Processo {processo.numero}',
        form=form,
        processo=processo
    )
