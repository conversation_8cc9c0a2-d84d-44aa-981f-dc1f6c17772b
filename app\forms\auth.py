from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON>ield, PasswordField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError
from app.models.usuario import Usuario

class LoginForm(FlaskForm):
    email = StringField('Email', validators=[DataRequired(), Email()])
    senha = PasswordField('Senha', validators=[DataRequired()])
    lembrar_me = BooleanField('Lembrar-me')
    submit = SubmitField('Entrar')

class RegistroForm(FlaskForm):
    nome = StringField('Nome Completo', validators=[DataRequired(), Length(min=3, max=100)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=100)])
    oab = StringField('Número OAB', validators=[DataRequired(), Length(max=20)])
    cargo = StringField('Cargo', validators=[Length(max=50)])
    telefone = StringField('Telefone', validators=[Length(max=20)])
    senha = PasswordField('Senha', validators=[DataRequired(), Length(min=6)])
    confirmar_senha = PasswordField('Confirmar Senha', validators=[DataRequired(), EqualTo('senha')])
    submit = SubmitField('Registrar')
    
    def validate_email(self, email):
        usuario = Usuario.query.filter_by(email=email.data).first()
        if usuario:
            raise ValidationError('Este email já está em uso. Por favor, use outro.')
    
    def validate_oab(self, oab):
        usuario = Usuario.query.filter_by(oab=oab.data).first()
        if usuario:
            raise ValidationError('Este número de OAB já está registrado.')

class PerfilForm(FlaskForm):
    nome = StringField('Nome Completo', validators=[DataRequired(), Length(min=3, max=100)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=100)])
    oab = StringField('Número OAB', validators=[DataRequired(), Length(max=20)])
    cargo = StringField('Cargo', validators=[Length(max=50)])
    telefone = StringField('Telefone', validators=[Length(max=20)])
    senha = PasswordField('Nova Senha', validators=[Length(min=6)])
    confirmar_senha = PasswordField('Confirmar Nova Senha', validators=[EqualTo('senha')])
    submit = SubmitField('Atualizar Perfil')
