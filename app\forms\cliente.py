from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, DateField, SubmitField
from wtforms.validators import DataRequired, Email, Length, Optional

class ClienteForm(FlaskForm):
    nome = StringField('Nome/Razão Social', validators=[DataRequired(), Length(max=100)])
    cpf_cnpj = StringField('CPF/CNPJ', validators=[DataRequired(), Length(max=20)])
    tipo = SelectField('Tipo', choices=[
        ('PF', 'Pessoa Física'),
        ('PJ', 'Pessoa Jurídica')
    ], default='PF')
    email = StringField('Email', validators=[Optional(), Email(), Length(max=100)])
    telefone = StringField('Telefone', validators=[Length(max=20)])
    celular = StringField('Celular', validators=[Length(max=20)])
    endereco = StringField('Endereço', validators=[Length(max=255)])
    cidade = StringField('Cidade', validators=[Length(max=100)])
    estado = SelectField('Estado', choices=[
        ('', 'Selecione'),
        ('AC', 'Acre'), ('AL', 'Alagoas'), ('AP', 'Amapá'), ('AM', 'Amazonas'),
        ('BA', 'Bahia'), ('CE', 'Ceará'), ('DF', 'Distrito Federal'), ('ES', 'Espírito Santo'),
        ('GO', 'Goiás'), ('MA', 'Maranhão'), ('MT', 'Mato Grosso'), ('MS', 'Mato Grosso do Sul'),
        ('MG', 'Minas Gerais'), ('PA', 'Pará'), ('PB', 'Paraíba'), ('PR', 'Paraná'),
        ('PE', 'Pernambuco'), ('PI', 'Piauí'), ('RJ', 'Rio de Janeiro'), ('RN', 'Rio Grande do Norte'),
        ('RS', 'Rio Grande do Sul'), ('RO', 'Rondônia'), ('RR', 'Roraima'), ('SC', 'Santa Catarina'),
        ('SP', 'São Paulo'), ('SE', 'Sergipe'), ('TO', 'Tocantins')
    ])
    cep = StringField('CEP', validators=[Length(max=10)])
    data_nascimento = DateField('Data de Nascimento', validators=[Optional()])
    observacoes = TextAreaField('Observações', validators=[Optional()])
    submit = SubmitField('Salvar')
