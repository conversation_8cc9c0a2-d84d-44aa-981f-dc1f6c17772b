from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired
from wtforms import <PERSON>Field, TextAreaField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length, Optional

class DocumentoForm(FlaskForm):
    nome = StringField('Nome do Documento', validators=[DataRequired(), Length(max=255)])
    tipo = SelectField('Tipo', choices=[
        ('Peti<PERSON>', 'Peti<PERSON>'),
        ('Contrato', 'Contrato'),
        ('Procuração', 'Procuração'),
        ('Decisão', 'Decisão'),
        ('Senten<PERSON>', 'Sentença'),
        ('Recurso', 'Recurso'),
        ('Outro', 'Outro')
    ])
    descricao = TextAreaField('Descrição', validators=[Optional()])
    arquivo = FileField('Arquivo', validators=[FileRequired()])
    processo_id = SelectField('Processo', coerce=int, validators=[Optional()])
    cliente_id = SelectField('Cliente', coerce=int, validators=[Optional()])
    submit = SubmitField('Salvar')

class ModeloDocumentoForm(FlaskForm):
    nome = StringField('Nome do Modelo', validators=[DataRequired(), Length(max=255)])
    descricao = TextAreaField('Descrição', validators=[Optional()])
    tipo = SelectField('Tipo', choices=[
        ('Petição Inicial', 'Petição Inicial'),
        ('Contestação', 'Contestação'),
        ('Recurso', 'Recurso'),
        ('Contrato', 'Contrato'),
        ('Procuração', 'Procuração'),
        ('Outro', 'Outro')
    ])
    arquivo = FileField('Arquivo Modelo', validators=[FileRequired()])
    campos_disponiveis = TextAreaField('Campos Disponíveis (JSON)', validators=[Optional()])
    submit = SubmitField('Salvar')

class GerarDocumentoForm(FlaskForm):
    modelo_id = SelectField('Modelo de Documento', coerce=int, validators=[DataRequired()])
    processo_id = SelectField('Processo', coerce=int, validators=[Optional()])
    cliente_id = SelectField('Cliente', coerce=int, validators=[Optional()])
    nome_documento = StringField('Nome do Documento Gerado', validators=[DataRequired(), Length(max=255)])
    campos = TextAreaField('Campos Personalizados (JSON)', validators=[Optional()])
    submit = SubmitField('Gerar Documento')
