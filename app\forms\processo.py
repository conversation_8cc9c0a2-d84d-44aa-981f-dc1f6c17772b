from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, DateField, FloatField, SubmitField
from wtforms.validators import DataRequired, Length, Optional
from datetime import datetime

class ProcessoForm(FlaskForm):
    numero = StringField('Número do Processo', validators=[DataRequired(), Length(max=50)])
    titulo = StringField('Título', validators=[DataRequired(), Length(max=255)])
    descricao = TextAreaField('Descrição', validators=[Optional()])
    tipo = SelectField('Tipo', choices=[
        ('Civil', 'Civil'),
        ('Trabalhista', 'Trabalhista'),
        ('Criminal', 'Criminal'),
        ('Tributário', 'Tributário'),
        ('Administrativo', 'Administrativo'),
        ('Previdenciário', 'Previdenciário'),
        ('Outro', 'Outro')
    ])
    area = StringField('Área', validators=[Length(max=50)])
    vara = StringField('Vara', validators=[Length(max=100)])
    comarca = StringField('Comarca', validators=[Length(max=100)])
    tribunal = StringField('Tribunal', validators=[Length(max=100)])
    valor_causa = FloatField('Valor da Causa', validators=[Optional()])
    status = SelectField('Status', choices=[
        ('Em andamento', 'Em andamento'),
        ('Concluído', 'Concluído'),
        ('Arquivado', 'Arquivado'),
        ('Suspenso', 'Suspenso'),
        ('Recurso', 'Recurso')
    ])
    data_inicio = DateField('Data de Início', validators=[DataRequired()], default=datetime.now)
    cliente_id = SelectField('Cliente', coerce=int, validators=[DataRequired()])
    submit = SubmitField('Salvar')

class AndamentoForm(FlaskForm):
    data = DateField('Data', validators=[DataRequired()], default=datetime.now)
    tipo = SelectField('Tipo', choices=[
        ('Audiência', 'Audiência'),
        ('Despacho', 'Despacho'),
        ('Sentença', 'Sentença'),
        ('Petição', 'Petição'),
        ('Recurso', 'Recurso'),
        ('Outro', 'Outro')
    ])
    descricao = TextAreaField('Descrição', validators=[DataRequired()])
    observacoes = TextAreaField('Observações', validators=[Optional()])
    submit = SubmitField('Salvar')

class PrazoForm(FlaskForm):
    titulo = StringField('Título', validators=[DataRequired(), Length(max=255)])
    descricao = TextAreaField('Descrição', validators=[Optional()])
    data_inicio = DateField('Data de Início', validators=[DataRequired()], default=datetime.now)
    data_fim = DateField('Data Final', validators=[DataRequired()])
    prioridade = SelectField('Prioridade', choices=[
        ('Alta', 'Alta'),
        ('Média', 'Média'),
        ('Baixa', 'Baixa')
    ], default='Média')
    submit = SubmitField('Salvar')
