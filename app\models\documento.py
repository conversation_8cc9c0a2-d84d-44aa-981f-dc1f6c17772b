from datetime import datetime
from app import db

class Documento(db.Model):
    __tablename__ = 'documentos'
    
    id = db.Column(db.Integer, primary_key=True)
    nome = db.Column(db.String(255), nullable=False)
    tipo = db.Column(db.String(50))  # Petição, Contrato, Procuração, etc.
    descricao = db.Column(db.Text)
    caminho_arquivo = db.Column(db.String(255), nullable=False)
    data_upload = db.Column(db.DateTime, default=datetime.utcnow)
    tamanho = db.Column(db.Integer)  # Tamanho em bytes
    formato = db.Column(db.String(10))  # PDF, DOCX, etc.
    
    # Chaves estrangeiras
    processo_id = db.Column(db.Integer, db.ForeignKey('processos.id'))
    cliente_id = db.Column(db.Integer, db.<PERSON><PERSON>('clientes.id'))
    
    def __repr__(self):
        return f'<Documento {self.nome}>'

class ModeloDocumento(db.Model):
    __tablename__ = 'modelos_documentos'
    
    id = db.Column(db.Integer, primary_key=True)
    nome = db.Column(db.String(255), nullable=False)
    descricao = db.Column(db.Text)
    tipo = db.Column(db.String(50), nullable=False)  # Petição Inicial, Contestação, Contrato, etc.
    caminho_arquivo = db.Column(db.String(255), nullable=False)
    data_criacao = db.Column(db.DateTime, default=datetime.utcnow)
    ultima_atualizacao = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    ativo = db.Column(db.Boolean, default=True)
    
    # Campos disponíveis para preenchimento no modelo
    campos_disponiveis = db.Column(db.Text)  # JSON com os campos disponíveis
    
    def __repr__(self):
        return f'<ModeloDocumento {self.nome}>'
