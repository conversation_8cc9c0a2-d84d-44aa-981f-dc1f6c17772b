from datetime import datetime
from app import db

class Processo(db.Model):
    __tablename__ = 'processos'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    numero = db.Column(db.String(50), unique=True, nullable=False)
    titulo = db.Column(db.String(255), nullable=False)
    descricao = db.Column(db.Text)
    tipo = db.Column(db.String(50))  # Civil, Trabalhista, Criminal, etc.
    area = db.Column(db.String(50))  # Área do direito
    vara = db.Column(db.String(100))
    comarca = db.Column(db.String(100))
    tribunal = db.Column(db.String(100))
    valor_causa = db.Column(db.Float)
    status = db.Column(db.String(50))  # Em andamento, Concluído, Arquivado, etc.
    data_inicio = db.Column(db.Date)
    data_conclusao = db.Column(db.Date)
    data_cadastro = db.Column(db.DateTime, default=datetime.utcnow)
    ultima_atualizacao = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Chaves estrangeiras
    cliente_id = db.Column(db.Integer, db.ForeignKey('clientes.id'), nullable=False)
    advogado_id = db.Column(db.Integer, db.ForeignKey('usuarios.id'), nullable=False)
    
    # Relacionamentos
    andamentos = db.relationship('Andamento', backref='processo', lazy='dynamic', cascade='all, delete-orphan')
    prazos = db.relationship('Prazo', backref='processo', lazy='dynamic', cascade='all, delete-orphan')
    documentos = db.relationship('Documento', backref='processo', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Processo {self.numero}>'

class Andamento(db.Model):
    __tablename__ = 'andamentos'
    
    id = db.Column(db.Integer, primary_key=True)
    data = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    descricao = db.Column(db.Text, nullable=False)
    tipo = db.Column(db.String(50))  # Audiência, Despacho, Sentença, etc.
    observacoes = db.Column(db.Text)
    
    # Chave estrangeira
    processo_id = db.Column(db.Integer, db.ForeignKey('processos.id'), nullable=False)
    
    def __repr__(self):
        return f'<Andamento {self.id} do Processo {self.processo_id}>'

class Prazo(db.Model):
    __tablename__ = 'prazos'
    
    id = db.Column(db.Integer, primary_key=True)
    titulo = db.Column(db.String(255), nullable=False)
    descricao = db.Column(db.Text)
    data_inicio = db.Column(db.DateTime, nullable=False)
    data_fim = db.Column(db.DateTime, nullable=False)
    concluido = db.Column(db.Boolean, default=False)
    prioridade = db.Column(db.String(20))  # Alta, Média, Baixa
    
    # Chave estrangeira
    processo_id = db.Column(db.Integer, db.ForeignKey('processos.id'), nullable=False)
    
    def __repr__(self):
        return f'<Prazo {self.titulo}>'
