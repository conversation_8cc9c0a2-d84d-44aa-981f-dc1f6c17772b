from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from app import db, login_manager

class Usuario(UserMixin, db.Model):
    __tablename__ = 'usuarios'
    
    id = db.Column(db.Integer, primary_key=True)
    nome = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    senha_hash = db.Column(db.String(128))
    oab = db.Column(db.String(20), unique=True)
    cargo = db.Column(db.String(50))
    telefone = db.Column(db.String(20))
    foto_perfil = db.Column(db.String(255))
    data_criacao = db.Column(db.DateTime, default=datetime.utcnow)
    ultimo_acesso = db.Column(db.DateTime)
    ativo = db.Column(db.<PERSON>, default=True)
    admin = db.Column(db.<PERSON>, default=False)
    
    # Relacionamentos
    processos = db.relationship('Processo', backref='advogado', lazy='dynamic')
    clientes = db.relationship('Cliente', backref='advogado_responsavel', lazy='dynamic')
    
    @property
    def senha(self):
        raise AttributeError('senha não é um atributo legível')
    
    @senha.setter
    def senha(self, senha):
        self.senha_hash = generate_password_hash(senha)
    
    def verificar_senha(self, senha):
        return check_password_hash(self.senha_hash, senha)
    
    def __repr__(self):
        return f'<Usuario {self.nome}>'

@login_manager.user_loader
def load_user(user_id):
    return Usuario.query.get(int(user_id))
