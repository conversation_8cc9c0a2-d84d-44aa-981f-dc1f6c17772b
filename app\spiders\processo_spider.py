import requests
from bs4 import BeautifulSoup
import re
import os
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class ProcessoSpider:
    """
    Spider para captura de informações de processos judiciais.
    Esta é uma classe base que pode ser estendida para diferentes tribunais.
    """
    
    def __init__(self, headless=True):
        """
        Inicializa o spider.
        
        Args:
            headless (bool): Se True, executa o navegador em modo headless (sem interface gráfica)
        """
        self.headless = headless
        self.driver = None
    
    def iniciar_driver(self):
        """
        Inicializa o driver do Selenium.
        """
        options = Options()
        if self.headless:
            options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        self.driver = webdriver.Chrome(options=options)
    
    def fechar_driver(self):
        """
        <PERSON>cha o driver do Selenium.
        """
        if self.driver:
            self.driver.quit()
            self.driver = None
    
    def consultar_processo(self, numero_processo):
        """
        Método base para consulta de processo. Deve ser implementado pelas subclasses.
        
        Args:
            numero_processo (str): Número do processo a ser consultado
            
        Returns:
            dict: Dicionário com as informações do processo
        """
        raise NotImplementedError("Este método deve ser implementado pelas subclasses")
    
    def baixar_documento(self, url, caminho_destino):
        """
        Baixa um documento a partir de uma URL.
        
        Args:
            url (str): URL do documento
            caminho_destino (str): Caminho onde o documento será salvo
            
        Returns:
            bool: True se o download foi bem-sucedido, False caso contrário
        """
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            with open(caminho_destino, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            return True
        except Exception as e:
            print(f"Erro ao baixar documento: {str(e)}")
            return False

class TJSPSpider(ProcessoSpider):
    """
    Spider específico para o Tribunal de Justiça de São Paulo (TJSP).
    """
    
    def __init__(self, headless=True):
        super().__init__(headless)
        self.base_url = "https://esaj.tjsp.jus.br/cpopg/open.do"
    
    def consultar_processo(self, numero_processo):
        """
        Consulta um processo no TJSP.
        
        Args:
            numero_processo (str): Número do processo no formato CNJ (NNNNNNN-NN.NNNN.N.NN.NNNN)
            
        Returns:
            dict: Dicionário com as informações do processo
        """
        try:
            # Inicializar o driver se ainda não foi inicializado
            if not self.driver:
                self.iniciar_driver()
            
            # Formatar o número do processo para o formato aceito pelo TJSP
            numero_formatado = self._formatar_numero_processo(numero_processo)
            
            # Acessar a página de consulta
            self.driver.get(self.base_url)
            
            # Preencher o campo de número do processo
            campo_processo = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "numeroDigitoAnoUnificado"))
            )
            campo_processo.clear()
            campo_processo.send_keys(numero_formatado)
            
            # Clicar no botão de consulta
            botao_consulta = self.driver.find_element(By.ID, "botaoConsultarProcessos")
            botao_consulta.click()
            
            # Aguardar o carregamento da página de detalhes
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "dadosProcesso"))
            )
            
            # Extrair informações do processo
            dados_processo = self._extrair_dados_processo()
            
            return dados_processo
            
        except Exception as e:
            print(f"Erro ao consultar processo: {str(e)}")
            return None
        finally:
            # Não fechar o driver aqui para permitir múltiplas consultas
            pass
    
    def _formatar_numero_processo(self, numero_processo):
        """
        Formata o número do processo para o formato aceito pelo TJSP.
        
        Args:
            numero_processo (str): Número do processo
            
        Returns:
            str: Número do processo formatado
        """
        # Remover caracteres não numéricos
        numero_limpo = re.sub(r'[^0-9]', '', numero_processo)
        
        # Verificar se o número tem o tamanho correto (20 dígitos)
        if len(numero_limpo) != 20:
            raise ValueError("Número de processo inválido")
        
        # Formatar no padrão NNNNNNN-NN.NNNN.N.NN.NNNN
        return f"{numero_limpo[0:7]}-{numero_limpo[7:9]}.{numero_limpo[9:13]}.{numero_limpo[13:14]}.{numero_limpo[14:16]}.{numero_limpo[16:20]}"
    
    def _extrair_dados_processo(self):
        """
        Extrai os dados do processo da página de detalhes.
        
        Returns:
            dict: Dicionário com as informações do processo
        """
        # Implementação simplificada - em um caso real, seria mais detalhada
        dados = {}
        
        # Extrair número do processo
        try:
            dados['numero'] = self.driver.find_element(By.ID, "numeroProcesso").text.strip()
        except:
            dados['numero'] = "Não encontrado"
        
        # Extrair classe
        try:
            dados['classe'] = self.driver.find_element(By.CSS_SELECTOR, "span.classeProcesso").text.strip()
        except:
            dados['classe'] = "Não encontrado"
        
        # Extrair assunto
        try:
            dados['assunto'] = self.driver.find_element(By.CSS_SELECTOR, "div.assuntoProcesso").text.strip()
        except:
            dados['assunto'] = "Não encontrado"
        
        # Extrair data de distribuição
        try:
            dados['data_distribuicao'] = self.driver.find_element(By.CSS_SELECTOR, "div.dataDistribuicao").text.strip()
        except:
            dados['data_distribuicao'] = "Não encontrado"
        
        # Extrair vara
        try:
            dados['vara'] = self.driver.find_element(By.CSS_SELECTOR, "div.secaoProcesso").text.strip()
        except:
            dados['vara'] = "Não encontrado"
        
        # Extrair partes do processo
        dados['partes'] = self._extrair_partes_processo()
        
        # Extrair movimentações
        dados['movimentacoes'] = self._extrair_movimentacoes()
        
        return dados
    
    def _extrair_partes_processo(self):
        """
        Extrai as partes do processo.
        
        Returns:
            dict: Dicionário com as partes do processo
        """
        partes = {}
        
        # Implementação simplificada
        try:
            tabela_partes = self.driver.find_element(By.ID, "tablePartesPrincipais")
            linhas = tabela_partes.find_elements(By.TAG_NAME, "tr")
            
            for linha in linhas:
                colunas = linha.find_elements(By.TAG_NAME, "td")
                if len(colunas) >= 2:
                    tipo_parte = colunas[0].text.strip()
                    nome_parte = colunas[1].text.strip()
                    partes[tipo_parte] = nome_parte
        except:
            partes['erro'] = "Não foi possível extrair as partes do processo"
        
        return partes
    
    def _extrair_movimentacoes(self):
        """
        Extrai as movimentações do processo.
        
        Returns:
            list: Lista de movimentações
        """
        movimentacoes = []
        
        # Implementação simplificada
        try:
            tabela_movimentacoes = self.driver.find_element(By.ID, "tabelaTodasMovimentacoes")
            linhas = tabela_movimentacoes.find_elements(By.TAG_NAME, "tr")
            
            for linha in linhas:
                colunas = linha.find_elements(By.TAG_NAME, "td")
                if len(colunas) >= 2:
                    data = colunas[0].text.strip()
                    descricao = colunas[1].text.strip()
                    movimentacoes.append({
                        'data': data,
                        'descricao': descricao
                    })
        except:
            movimentacoes.append({
                'data': 'Erro',
                'descricao': 'Não foi possível extrair as movimentações do processo'
            })
        
        return movimentacoes
