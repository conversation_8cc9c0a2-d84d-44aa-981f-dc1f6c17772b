/* Cores do tema para escritório de advocacia */
:root {
    --primary-color: #0A3161;    /* Azul marinho */
    --secondary-color: #D4AF37;  /* Dourado */
    --accent-color: #4A4A4A;     /* Cinza escuro */
    --text-color: #333333;       /* Quase preto */
    --bg-color: #F5F5F5;         /* Cinza claro */
    --light-color: #FFFFFF;      /* Branco */
    --danger-color: #DC3545;     /* Vermelho */
    --success-color: #28A745;    /* Verde */
    --warning-color: #FFC107;    /* <PERSON><PERSON> */
    --info-color: #17A2B8;       /* Azul claro */
}

body {
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    color: var(--text-color);
    background-color: var(--bg-color);
}

/* Navbar personalizada */
.navbar-dark.bg-primary {
    background-color: var(--primary-color) !important;
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 0.5px;
}

/* Cards */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
    font-weight: 600;
}

/* Botões */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #082a54;
    border-color: #082a54;
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--text-color);
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: #c09c30;
    border-color: #c09c30;
    color: var(--text-color);
}

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    color: #082a54;
    text-decoration: underline;
}

/* Tabelas */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table-hover tbody tr:hover {
    background-color: rgba(10, 49, 97, 0.05);
}

/* Formulários */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(10, 49, 97, 0.25);
}

.form-label {
    font-weight: 500;
}

/* Badges */
.badge {
    padding: 0.4em 0.6em;
    font-weight: 500;
}

/* Footer */
.footer {
    background-color: var(--primary-color) !important;
    color: var(--light-color);
}

.footer .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Responsividade */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Animações */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Ícones */
.icon-gold {
    color: var(--secondary-color);
}

/* Utilitários */
.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

/* Estilos específicos para páginas */
.login-page, .register-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
}

/* Dashboard */
.dashboard-card {
    transition: transform 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
}
