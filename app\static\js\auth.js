/**
 * Funções de autenticação para o CRM Advocacia
 */

// Usuários de demonstração (em um sistema real, isso seria armazenado no banco de dados)
const usuariosDemostracao = [
    {
        id: 1,
        nome: 'Administrador',
        email: '<EMAIL>',
        senha: 'admin123',
        oab: '123456',
        cargo: 'Administrador',
        telefone: '(11) 99999-9999',
        admin: true,
        ativo: true,
        ultimo_acesso: '2023-12-01T10:30:00'
    },
    {
        id: 2,
        nome: '<PERSON>',
        email: '<EMAIL>',
        senha: 'advogado123',
        oab: '654321',
        cargo: 'Advogado',
        telefone: '(11) 88888-8888',
        admin: false,
        ativo: true,
        ultimo_acesso: '2023-12-02T14:45:00'
    },
    {
        id: 3,
        nome: '<PERSON>',
        email: '<EMAIL>',
        senha: 'maria123',
        oab: '789012',
        cargo: 'Advogada',
        telefone: '(11) 77777-7777',
        admin: false,
        ativo: true,
        ultimo_acesso: null
    },
    {
        id: 4,
        nome: '<PERSON>',
        email: '<EMAIL>',
        senha: 'carlos123',
        oab: '345678',
        cargo: 'Advogado Sênior',
        telefone: '(11) 66666-6666',
        admin: false,
        ativo: false,
        ultimo_acesso: '2023-11-15T09:20:00'
    }
];

/**
 * Verifica as credenciais do usuário
 * @param {string} email - Email do usuário
 * @param {string} senha - Senha do usuário
 * @returns {Object|null} - Objeto com os dados do usuário ou null se as credenciais forem inválidas
 */
function verificarCredenciais(email, senha) {
    // Em um sistema real, isso seria uma chamada AJAX para o backend
    const usuario = usuariosDemostracao.find(u => u.email === email && u.senha === senha);

    if (usuario) {
        // Não retornar a senha
        const { senha, ...usuarioSemSenha } = usuario;
        return usuarioSemSenha;
    }

    return null;
}

/**
 * Realiza o login do usuário
 * @param {string} email - Email do usuário
 * @param {string} senha - Senha do usuário
 * @param {boolean} lembrarMe - Se deve lembrar o usuário
 * @returns {Promise} - Promise com o resultado do login
 */
function realizarLogin(email, senha, lembrarMe = false) {
    return new Promise((resolve, reject) => {
        // Simular uma chamada assíncrona
        setTimeout(() => {
            const usuario = verificarCredenciais(email, senha);

            if (usuario) {
                // Verificar se o usuário está ativo
                if (!usuario.ativo) {
                    reject({
                        success: false,
                        message: 'Sua conta está desativada. Entre em contato com o administrador.'
                    });
                    return;
                }

                // Salvar dados do usuário no localStorage ou sessionStorage
                if (lembrarMe) {
                    localStorage.setItem('usuario', JSON.stringify(usuario));
                } else {
                    sessionStorage.setItem('usuario', JSON.stringify(usuario));
                }

                // Atualizar último acesso
                usuario.ultimo_acesso = new Date().toISOString();

                resolve({
                    success: true,
                    message: 'Login realizado com sucesso!',
                    usuario
                });
            } else {
                reject({
                    success: false,
                    message: 'Email ou senha inválidos'
                });
            }
        }, 500); // Simular um pequeno atraso
    });
}

/**
 * Realiza o logout do usuário
 */
function realizarLogout() {
    // Remover dados do usuário do localStorage e sessionStorage
    localStorage.removeItem('usuario');
    sessionStorage.removeItem('usuario');

    // Redirecionar para a página de login
    window.location.href = 'login.html';
}

/**
 * Verifica se o usuário está logado
 * @returns {boolean} - Se o usuário está logado
 */
function estaLogado() {
    return !!(localStorage.getItem('usuario') || sessionStorage.getItem('usuario'));
}

/**
 * Obtém os dados do usuário logado
 * @returns {Object|null} - Objeto com os dados do usuário ou null se não estiver logado
 */
function obterUsuarioLogado() {
    const usuarioLocalStorage = localStorage.getItem('usuario');
    const usuarioSessionStorage = sessionStorage.getItem('usuario');

    if (usuarioLocalStorage) {
        return JSON.parse(usuarioLocalStorage);
    }

    if (usuarioSessionStorage) {
        return JSON.parse(usuarioSessionStorage);
    }

    return null;
}

/**
 * Verifica se o usuário é administrador
 * @returns {boolean} - Se o usuário é administrador
 */
function eAdmin() {
    const usuario = obterUsuarioLogado();
    return usuario ? usuario.admin : false;
}

/**
 * Cria um novo usuário (apenas para administradores)
 * @param {Object} dadosUsuario - Dados do novo usuário
 * @returns {Promise} - Promise com o resultado da criação
 */
function criarUsuario(dadosUsuario) {
    return new Promise((resolve, reject) => {
        // Verificar se o usuário logado é administrador
        if (!eAdmin()) {
            reject({
                success: false,
                message: 'Você não tem permissão para criar usuários.'
            });
            return;
        }

        // Simular uma chamada assíncrona
        setTimeout(() => {
            try {
                // Verificar se o email já existe
                const emailExistente = usuariosDemostracao.some(u => u.email === dadosUsuario.email);
                if (emailExistente) {
                    reject({
                        success: false,
                        message: 'Este email já está em uso.'
                    });
                    return;
                }

                // Verificar se o OAB já existe
                const oabExistente = usuariosDemostracao.some(u => u.oab === dadosUsuario.oab);
                if (oabExistente) {
                    reject({
                        success: false,
                        message: 'Este número de OAB já está registrado.'
                    });
                    return;
                }

                // Criar novo usuário
                const novoUsuario = {
                    id: usuariosDemostracao.length + 1,
                    nome: dadosUsuario.nome,
                    email: dadosUsuario.email,
                    senha: dadosUsuario.senha,
                    oab: dadosUsuario.oab,
                    cargo: dadosUsuario.cargo,
                    telefone: dadosUsuario.telefone,
                    admin: dadosUsuario.tipo === 'admin',
                    ativo: dadosUsuario.ativo || true,
                    ultimo_acesso: null
                };

                // Adicionar à lista de usuários
                usuariosDemostracao.push(novoUsuario);

                resolve({
                    success: true,
                    message: 'Usuário criado com sucesso!'
                });
            } catch (error) {
                reject({
                    success: false,
                    message: `Erro ao criar usuário: ${error.message}`
                });
            }
        }, 500); // Simular um pequeno atraso
    });
}
