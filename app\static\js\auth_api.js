/**
 * Funções de autenticação para o CRM Advocacia usando a API real
 */

// URL base da API
const API_BASE_URL = '/api';

/**
 * Realiza o login do usuário
 * @param {string} email - Email do usuário
 * @param {string} senha - Senha do usuário
 * @param {boolean} lembrarMe - Se deve lembrar o usuário
 * @returns {Promise} - Promise com o resultado do login
 */
async function realizarLogin(email, senha, lembrarMe = false) {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, senha }),
            credentials: 'include' // Incluir cookies na requisição
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erro ao fazer login');
        }
        
        // Salvar dados do usuário no localStorage ou sessionStorage
        if (lembrarMe) {
            localStorage.setItem('usuario', JSON.stringify(data.usuario));
        } else {
            sessionStorage.setItem('usuario', JSON.stringify(data.usuario));
        }
        
        return {
            success: true,
            message: data.message,
            usuario: data.usuario
        };
    } catch (error) {
        return Promise.reject({
            success: false,
            message: error.message
        });
    }
}

/**
 * Realiza o logout do usuário
 * @returns {Promise} - Promise com o resultado do logout
 */
async function realizarLogout() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/logout`, {
            method: 'POST',
            credentials: 'include' // Incluir cookies na requisição
        });
        
        const data = await response.json();
        
        // Remover dados do usuário do localStorage e sessionStorage
        localStorage.removeItem('usuario');
        sessionStorage.removeItem('usuario');
        
        return {
            success: true,
            message: data.message
        };
    } catch (error) {
        // Mesmo em caso de erro, remover dados do usuário
        localStorage.removeItem('usuario');
        sessionStorage.removeItem('usuario');
        
        return Promise.reject({
            success: false,
            message: error.message
        });
    }
}

/**
 * Verifica se o usuário está logado
 * @returns {boolean} - Se o usuário está logado
 */
function estaLogado() {
    return !!(localStorage.getItem('usuario') || sessionStorage.getItem('usuario'));
}

/**
 * Obtém os dados do usuário logado
 * @returns {Object|null} - Objeto com os dados do usuário ou null se não estiver logado
 */
function obterUsuarioLogado() {
    const usuarioLocalStorage = localStorage.getItem('usuario');
    const usuarioSessionStorage = sessionStorage.getItem('usuario');
    
    if (usuarioLocalStorage) {
        return JSON.parse(usuarioLocalStorage);
    }
    
    if (usuarioSessionStorage) {
        return JSON.parse(usuarioSessionStorage);
    }
    
    return null;
}

/**
 * Verifica se o usuário é administrador
 * @returns {boolean} - Se o usuário é administrador
 */
function eAdmin() {
    const usuario = obterUsuarioLogado();
    return usuario ? usuario.admin : false;
}

/**
 * Obtém a lista de usuários (apenas para administradores)
 * @returns {Promise} - Promise com a lista de usuários
 */
async function obterUsuarios() {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/usuarios`, {
            method: 'GET',
            credentials: 'include' // Incluir cookies na requisição
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erro ao obter usuários');
        }
        
        return data.usuarios;
    } catch (error) {
        return Promise.reject({
            success: false,
            message: error.message
        });
    }
}

/**
 * Cria um novo usuário (apenas para administradores)
 * @param {Object} dadosUsuario - Dados do novo usuário
 * @returns {Promise} - Promise com o resultado da criação
 */
async function criarUsuario(dadosUsuario) {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/usuarios`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dadosUsuario),
            credentials: 'include' // Incluir cookies na requisição
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erro ao criar usuário');
        }
        
        return {
            success: true,
            message: data.message,
            id: data.id
        };
    } catch (error) {
        return Promise.reject({
            success: false,
            message: error.message
        });
    }
}

/**
 * Atualiza um usuário existente
 * @param {number} id - ID do usuário
 * @param {Object} dadosUsuario - Novos dados do usuário
 * @returns {Promise} - Promise com o resultado da atualização
 */
async function atualizarUsuario(id, dadosUsuario) {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/usuarios/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dadosUsuario),
            credentials: 'include' // Incluir cookies na requisição
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erro ao atualizar usuário');
        }
        
        // Se o usuário atualizou seu próprio perfil, atualizar no storage
        const usuarioLogado = obterUsuarioLogado();
        if (usuarioLogado && usuarioLogado.id === id) {
            const novoUsuario = { ...usuarioLogado, ...dadosUsuario };
            
            if (localStorage.getItem('usuario')) {
                localStorage.setItem('usuario', JSON.stringify(novoUsuario));
            }
            
            if (sessionStorage.getItem('usuario')) {
                sessionStorage.setItem('usuario', JSON.stringify(novoUsuario));
            }
        }
        
        return {
            success: true,
            message: data.message
        };
    } catch (error) {
        return Promise.reject({
            success: false,
            message: error.message
        });
    }
}

/**
 * Exclui um usuário
 * @param {number} id - ID do usuário a ser excluído
 * @returns {Promise} - Promise com o resultado da exclusão
 */
async function excluirUsuario(id) {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/usuarios/${id}`, {
            method: 'DELETE',
            credentials: 'include' // Incluir cookies na requisição
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erro ao excluir usuário');
        }
        
        return {
            success: true,
            message: data.message
        };
    } catch (error) {
        return Promise.reject({
            success: false,
            message: error.message
        });
    }
}

/**
 * Envia um link de redefinição de senha para o email do usuário
 * @param {string} email - Email do usuário
 * @returns {Promise} - Promise com o resultado do envio
 */
async function enviarLinkRedefinicaoSenha(email) {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/redefinir-senha`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email }),
            credentials: 'include' // Incluir cookies na requisição
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erro ao enviar link de redefinição');
        }
        
        return {
            success: true,
            message: data.message
        };
    } catch (error) {
        return Promise.reject({
            success: false,
            message: error.message
        });
    }
}

/**
 * Redefine a senha do usuário usando um token
 * @param {number} id - ID do usuário
 * @param {string} token - Token de redefinição
 * @param {string} senha - Nova senha
 * @returns {Promise} - Promise com o resultado da redefinição
 */
async function redefinirSenha(id, token, senha) {
    try {
        const response = await fetch(`${API_BASE_URL}/auth/confirmar-redefinicao`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id, token, senha }),
            credentials: 'include' // Incluir cookies na requisição
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Erro ao redefinir senha');
        }
        
        return {
            success: true,
            message: data.message
        };
    } catch (error) {
        return Promise.reject({
            success: false,
            message: error.message
        });
    }
}
