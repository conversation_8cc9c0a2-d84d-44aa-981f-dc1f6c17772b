// Funções utilitárias para o CRM Advocacia

// Função para formatar CPF/CNPJ
function formatarCpfCnpj(valor) {
    valor = valor.replace(/\D/g, '');
    
    if (valor.length <= 11) {
        // CPF
        valor = valor.replace(/(\d{3})(\d)/, '$1.$2');
        valor = valor.replace(/(\d{3})(\d)/, '$1.$2');
        valor = valor.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
    } else {
        // CNPJ
        valor = valor.replace(/^(\d{2})(\d)/, '$1.$2');
        valor = valor.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
        valor = valor.replace(/\.(\d{3})(\d)/, '.$1/$2');
        valor = valor.replace(/(\d{4})(\d)/, '$1-$2');
    }
    
    return valor;
}

// Função para formatar telefone
function formatarTelefone(valor) {
    valor = valor.replace(/\D/g, '');
    
    if (valor.length <= 10) {
        // Telefone fixo
        valor = valor.replace(/(\d{2})(\d)/, '($1) $2');
        valor = valor.replace(/(\d{4})(\d)/, '$1-$2');
    } else {
        // Celular
        valor = valor.replace(/(\d{2})(\d)/, '($1) $2');
        valor = valor.replace(/(\d{5})(\d)/, '$1-$2');
    }
    
    return valor;
}

// Função para formatar CEP
function formatarCep(valor) {
    valor = valor.replace(/\D/g, '');
    valor = valor.replace(/^(\d{5})(\d)/, '$1-$2');
    return valor;
}

// Função para formatar número de processo
function formatarNumeroProcesso(valor) {
    valor = valor.replace(/\D/g, '');
    
    if (valor.length <= 20) {
        // Formato CNJ: NNNNNNN-NN.NNNN.N.NN.NNNN
        valor = valor.replace(/(\d{7})(\d)/, '$1-$2');
        valor = valor.replace(/(\d{7})-(\d{2})(\d)/, '$1-$2.$3');
        valor = valor.replace(/(\d{7})-(\d{2}).(\d{4})(\d)/, '$1-$2.$3.$4');
        valor = valor.replace(/(\d{7})-(\d{2}).(\d{4}).(\d{1})(\d)/, '$1-$2.$3.$4.$5');
        valor = valor.replace(/(\d{7})-(\d{2}).(\d{4}).(\d{1}).(\d{2})(\d)/, '$1-$2.$3.$4.$5.$6');
    }
    
    return valor;
}

// Inicialização quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar tooltips do Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Inicializar popovers do Bootstrap
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Aplicar máscaras aos campos de formulário
    const cpfCnpjInputs = document.querySelectorAll('.cpf-cnpj-mask');
    cpfCnpjInputs.forEach(input => {
        input.addEventListener('input', function() {
            this.value = formatarCpfCnpj(this.value);
        });
    });
    
    const telefoneInputs = document.querySelectorAll('.telefone-mask');
    telefoneInputs.forEach(input => {
        input.addEventListener('input', function() {
            this.value = formatarTelefone(this.value);
        });
    });
    
    const cepInputs = document.querySelectorAll('.cep-mask');
    cepInputs.forEach(input => {
        input.addEventListener('input', function() {
            this.value = formatarCep(this.value);
        });
    });
    
    const processoInputs = document.querySelectorAll('.processo-mask');
    processoInputs.forEach(input => {
        input.addEventListener('input', function() {
            this.value = formatarNumeroProcesso(this.value);
        });
    });
    
    // Fechar alertas automaticamente após 5 segundos
    const alertas = document.querySelectorAll('.alert:not(.alert-permanent)');
    alertas.forEach(alerta => {
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alerta);
            bsAlert.close();
        }, 5000);
    });
});

// Função para confirmar exclusão
function confirmarExclusao(event, mensagem) {
    if (!confirm(mensagem || 'Tem certeza que deseja excluir este item?')) {
        event.preventDefault();
        return false;
    }
    return true;
}

// Função para buscar CEP via API
function buscarCep(cep) {
    cep = cep.replace(/\D/g, '');
    
    if (cep.length !== 8) {
        return;
    }
    
    fetch(`https://viacep.com.br/ws/${cep}/json/`)
        .then(response => response.json())
        .then(data => {
            if (!data.erro) {
                document.querySelector('#endereco').value = data.logradouro;
                document.querySelector('#cidade').value = data.localidade;
                document.querySelector('#estado').value = data.uf;
                document.querySelector('#bairro').value = data.bairro;
            }
        })
        .catch(error => console.error('Erro ao buscar CEP:', error));
}
