/**
 * Funções para gerenciamento de usuários do CRM Advocacia
 */

// Verificar se o usuário está logado e é administrador
document.addEventListener('DOMContentLoaded', function() {
    // Verificar se o usuário está logado
    if (!estaLogado()) {
        window.location.href = 'login.html';
        return;
    }
    
    // Verificar se o usuário é administrador
    const usuario = obterUsuarioLogado();
    if (!usuario || !usuario.admin) {
        alert('Você não tem permissão para acessar esta página.');
        window.location.href = 'demo.html';
        return;
    }
    
    // Atualizar nome do usuário na barra de navegação
    const userDropdown = document.getElementById('userDropdown');
    if (userDropdown && usuario) {
        userDropdown.innerHTML = `<i class="fas fa-user-circle me-1"></i>${usuario.nome}`;
    }
    
    // Configurar botão de logout
    const logoutButton = document.querySelector('a[href="login.html"]');
    if (logoutButton) {
        logoutButton.addEventListener('click', function(e) {
            e.preventDefault();
            realizarLogout();
        });
    }
    
    // Inicializar DataTable
    inicializarTabelaUsuarios();
    
    // Configurar eventos dos modais
    configurarModais();
});

/**
 * Inicializa a tabela de usuários com DataTables
 */
function inicializarTabelaUsuarios() {
    // Obter usuários (em um sistema real, isso seria uma chamada AJAX)
    const usuarios = obterUsuarios();
    
    // Preencher a tabela
    const tabela = $('#tabelaUsuarios').DataTable({
        data: usuarios,
        columns: [
            { data: 'nome' },
            { data: 'email' },
            { data: 'oab' },
            { data: 'cargo' },
            { data: 'telefone' },
            { 
                data: 'admin',
                render: function(data) {
                    return data ? 'Administrador' : 'Advogado';
                }
            },
            { 
                data: 'ativo',
                render: function(data) {
                    return data ? 
                        '<span class="status-ativo"><i class="fas fa-check-circle me-1"></i>Ativo</span>' : 
                        '<span class="status-inativo"><i class="fas fa-times-circle me-1"></i>Inativo</span>';
                }
            },
            { 
                data: 'ultimo_acesso',
                render: function(data) {
                    if (!data) return 'Nunca';
                    const data_formatada = new Date(data).toLocaleString('pt-BR');
                    return data_formatada;
                }
            },
            { 
                data: 'id',
                render: function(data, type, row) {
                    return `
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-primary btn-editar" data-id="${data}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-warning btn-redefinir" data-id="${data}" data-email="${row.email}">
                                <i class="fas fa-key"></i>
                            </button>
                            <button type="button" class="btn btn-danger btn-excluir" data-id="${data}">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        </div>
                    `;
                }
            }
        ],
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/pt-BR.json'
        },
        responsive: true,
        order: [[0, 'asc']]
    });
    
    // Configurar eventos dos botões
    $('#tabelaUsuarios').on('click', '.btn-editar', function() {
        const id = $(this).data('id');
        abrirModalEditarUsuario(id);
    });
    
    $('#tabelaUsuarios').on('click', '.btn-redefinir', function() {
        const id = $(this).data('id');
        const email = $(this).data('email');
        abrirModalRedefinirSenha(id, email);
    });
    
    $('#tabelaUsuarios').on('click', '.btn-excluir', function() {
        const id = $(this).data('id');
        confirmarExclusaoUsuario(id);
    });
}

/**
 * Configura os eventos dos modais
 */
function configurarModais() {
    // Modal de Novo Usuário
    const btnSalvarUsuario = document.getElementById('btnSalvarUsuario');
    const novoUsuarioForm = document.getElementById('novoUsuarioForm');
    const novoUsuarioModal = document.getElementById('novoUsuarioModal');
    
    if (btnSalvarUsuario && novoUsuarioForm && novoUsuarioModal) {
        btnSalvarUsuario.addEventListener('click', function() {
            // Verificar se o formulário é válido
            if (novoUsuarioForm.checkValidity()) {
                // Verificar se as senhas coincidem
                const senha = document.getElementById('senha').value;
                const confirmarSenha = document.getElementById('confirmarSenha').value;
                
                if (senha !== confirmarSenha) {
                    alert('As senhas não coincidem!');
                    return;
                }
                
                // Obter dados do formulário
                const dadosUsuario = {
                    nome: document.getElementById('nome').value,
                    email: document.getElementById('email').value,
                    oab: document.getElementById('oab').value,
                    cargo: document.getElementById('cargo').value,
                    telefone: document.getElementById('telefone').value,
                    tipo: document.getElementById('tipo').value,
                    senha: senha,
                    ativo: document.getElementById('ativo').checked
                };
                
                // Desabilitar o botão durante o processo
                const originalButtonText = btnSalvarUsuario.innerHTML;
                btnSalvarUsuario.disabled = true;
                btnSalvarUsuario.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Salvando...';
                
                // Criar usuário
                criarUsuario(dadosUsuario)
                    .then(result => {
                        // Usuário criado com sucesso
                        alert(result.message);
                        
                        // Fechar o modal
                        const modal = bootstrap.Modal.getInstance(novoUsuarioModal);
                        modal.hide();
                        
                        // Limpar o formulário
                        novoUsuarioForm.reset();
                        
                        // Recarregar a tabela
                        recarregarTabela();
                    })
                    .catch(error => {
                        // Erro ao criar usuário
                        alert(error.message);
                    })
                    .finally(() => {
                        // Restaurar o botão
                        btnSalvarUsuario.disabled = false;
                        btnSalvarUsuario.innerHTML = originalButtonText;
                    });
            } else {
                // Forçar a validação do formulário
                novoUsuarioForm.reportValidity();
            }
        });
    }
    
    // Modal de Editar Usuário
    const btnAtualizarUsuario = document.getElementById('btnAtualizarUsuario');
    const editarUsuarioForm = document.getElementById('editarUsuarioForm');
    const editarUsuarioModal = document.getElementById('editarUsuarioModal');
    
    if (btnAtualizarUsuario && editarUsuarioForm && editarUsuarioModal) {
        btnAtualizarUsuario.addEventListener('click', function() {
            // Verificar se o formulário é válido
            if (editarUsuarioForm.checkValidity()) {
                // Obter dados do formulário
                const id = document.getElementById('editarId').value;
                const dadosUsuario = {
                    nome: document.getElementById('editarNome').value,
                    email: document.getElementById('editarEmail').value,
                    oab: document.getElementById('editarOab').value,
                    cargo: document.getElementById('editarCargo').value,
                    telefone: document.getElementById('editarTelefone').value,
                    tipo: document.getElementById('editarTipo').value,
                    ativo: document.getElementById('editarAtivo').checked
                };
                
                // Desabilitar o botão durante o processo
                const originalButtonText = btnAtualizarUsuario.innerHTML;
                btnAtualizarUsuario.disabled = true;
                btnAtualizarUsuario.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Salvando...';
                
                // Atualizar usuário
                atualizarUsuario(id, dadosUsuario)
                    .then(result => {
                        // Usuário atualizado com sucesso
                        alert(result.message);
                        
                        // Fechar o modal
                        const modal = bootstrap.Modal.getInstance(editarUsuarioModal);
                        modal.hide();
                        
                        // Recarregar a tabela
                        recarregarTabela();
                    })
                    .catch(error => {
                        // Erro ao atualizar usuário
                        alert(error.message);
                    })
                    .finally(() => {
                        // Restaurar o botão
                        btnAtualizarUsuario.disabled = false;
                        btnAtualizarUsuario.innerHTML = originalButtonText;
                    });
            } else {
                // Forçar a validação do formulário
                editarUsuarioForm.reportValidity();
            }
        });
    }
    
    // Modal de Redefinir Senha
    const btnEnviarRedefinicao = document.getElementById('btnEnviarRedefinicao');
    const redefinirSenhaForm = document.getElementById('redefinirSenhaForm');
    const redefinirSenhaModal = document.getElementById('redefinirSenhaModal');
    
    if (btnEnviarRedefinicao && redefinirSenhaForm && redefinirSenhaModal) {
        btnEnviarRedefinicao.addEventListener('click', function() {
            // Obter dados do formulário
            const id = document.getElementById('redefinirId').value;
            const email = document.getElementById('redefinirEmail').value;
            
            // Desabilitar o botão durante o processo
            const originalButtonText = btnEnviarRedefinicao.innerHTML;
            btnEnviarRedefinicao.disabled = true;
            btnEnviarRedefinicao.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Enviando...';
            
            // Enviar link de redefinição
            enviarLinkRedefinicaoSenha(id, email)
                .then(result => {
                    // Link enviado com sucesso
                    alert(result.message);
                    
                    // Fechar o modal
                    const modal = bootstrap.Modal.getInstance(redefinirSenhaModal);
                    modal.hide();
                })
                .catch(error => {
                    // Erro ao enviar link
                    alert(error.message);
                })
                .finally(() => {
                    // Restaurar o botão
                    btnEnviarRedefinicao.disabled = false;
                    btnEnviarRedefinicao.innerHTML = originalButtonText;
                });
        });
    }
}

/**
 * Abre o modal de edição de usuário
 * @param {number} id - ID do usuário a ser editado
 */
function abrirModalEditarUsuario(id) {
    // Obter dados do usuário
    const usuario = obterUsuarioPorId(id);
    
    if (!usuario) {
        alert('Usuário não encontrado.');
        return;
    }
    
    // Preencher o formulário
    document.getElementById('editarId').value = usuario.id;
    document.getElementById('editarNome').value = usuario.nome;
    document.getElementById('editarEmail').value = usuario.email;
    document.getElementById('editarOab').value = usuario.oab;
    document.getElementById('editarCargo').value = usuario.cargo || '';
    document.getElementById('editarTelefone').value = usuario.telefone || '';
    document.getElementById('editarTipo').value = usuario.admin ? 'admin' : 'advogado';
    document.getElementById('editarAtivo').checked = usuario.ativo;
    
    // Abrir o modal
    const modal = new bootstrap.Modal(document.getElementById('editarUsuarioModal'));
    modal.show();
}

/**
 * Abre o modal de redefinição de senha
 * @param {number} id - ID do usuário
 * @param {string} email - Email do usuário
 */
function abrirModalRedefinirSenha(id, email) {
    // Preencher o formulário
    document.getElementById('redefinirId').value = id;
    document.getElementById('redefinirEmail').value = email;
    
    // Abrir o modal
    const modal = new bootstrap.Modal(document.getElementById('redefinirSenhaModal'));
    modal.show();
}

/**
 * Confirma a exclusão de um usuário
 * @param {number} id - ID do usuário a ser excluído
 */
function confirmarExclusaoUsuario(id) {
    // Obter dados do usuário
    const usuario = obterUsuarioPorId(id);
    
    if (!usuario) {
        alert('Usuário não encontrado.');
        return;
    }
    
    // Confirmar exclusão
    if (confirm(`Tem certeza que deseja excluir o usuário ${usuario.nome}?`)) {
        excluirUsuario(id)
            .then(result => {
                // Usuário excluído com sucesso
                alert(result.message);
                
                // Recarregar a tabela
                recarregarTabela();
            })
            .catch(error => {
                // Erro ao excluir usuário
                alert(error.message);
            });
    }
}

/**
 * Recarrega a tabela de usuários
 */
function recarregarTabela() {
    // Destruir a tabela atual
    $('#tabelaUsuarios').DataTable().destroy();
    
    // Inicializar novamente
    inicializarTabelaUsuarios();
}

// Funções de API (simuladas para demonstração)

/**
 * Obtém a lista de usuários
 * @returns {Array} - Lista de usuários
 */
function obterUsuarios() {
    // Em um sistema real, isso seria uma chamada AJAX para o backend
    return usuariosDemostracao;
}

/**
 * Obtém um usuário pelo ID
 * @param {number} id - ID do usuário
 * @returns {Object|null} - Objeto com os dados do usuário ou null se não encontrado
 */
function obterUsuarioPorId(id) {
    // Em um sistema real, isso seria uma chamada AJAX para o backend
    const usuario = usuariosDemostracao.find(u => u.id === parseInt(id));
    
    if (usuario) {
        // Não retornar a senha
        const { senha, ...usuarioSemSenha } = usuario;
        return usuarioSemSenha;
    }
    
    return null;
}

/**
 * Atualiza os dados de um usuário
 * @param {number} id - ID do usuário
 * @param {Object} dadosUsuario - Novos dados do usuário
 * @returns {Promise} - Promise com o resultado da atualização
 */
function atualizarUsuario(id, dadosUsuario) {
    return new Promise((resolve, reject) => {
        // Verificar se o usuário logado é administrador
        if (!eAdmin()) {
            reject({
                success: false,
                message: 'Você não tem permissão para editar usuários.'
            });
            return;
        }
        
        // Simular uma chamada assíncrona
        setTimeout(() => {
            try {
                // Encontrar o usuário
                const index = usuariosDemostracao.findIndex(u => u.id === parseInt(id));
                
                if (index === -1) {
                    reject({
                        success: false,
                        message: 'Usuário não encontrado.'
                    });
                    return;
                }
                
                // Verificar se o email já existe e não é do usuário atual
                const emailExistente = usuariosDemostracao.some(u => u.email === dadosUsuario.email && u.id !== parseInt(id));
                if (emailExistente) {
                    reject({
                        success: false,
                        message: 'Este email já está em uso.'
                    });
                    return;
                }
                
                // Verificar se o OAB já existe e não é do usuário atual
                const oabExistente = usuariosDemostracao.some(u => u.oab === dadosUsuario.oab && u.id !== parseInt(id));
                if (oabExistente) {
                    reject({
                        success: false,
                        message: 'Este número de OAB já está registrado.'
                    });
                    return;
                }
                
                // Atualizar usuário
                usuariosDemostracao[index] = {
                    ...usuariosDemostracao[index],
                    nome: dadosUsuario.nome,
                    email: dadosUsuario.email,
                    oab: dadosUsuario.oab,
                    cargo: dadosUsuario.cargo,
                    telefone: dadosUsuario.telefone,
                    admin: dadosUsuario.tipo === 'admin',
                    ativo: dadosUsuario.ativo
                };
                
                resolve({
                    success: true,
                    message: 'Usuário atualizado com sucesso!'
                });
            } catch (error) {
                reject({
                    success: false,
                    message: `Erro ao atualizar usuário: ${error.message}`
                });
            }
        }, 500); // Simular um pequeno atraso
    });
}

/**
 * Exclui um usuário
 * @param {number} id - ID do usuário a ser excluído
 * @returns {Promise} - Promise com o resultado da exclusão
 */
function excluirUsuario(id) {
    return new Promise((resolve, reject) => {
        // Verificar se o usuário logado é administrador
        if (!eAdmin()) {
            reject({
                success: false,
                message: 'Você não tem permissão para excluir usuários.'
            });
            return;
        }
        
        // Simular uma chamada assíncrona
        setTimeout(() => {
            try {
                // Encontrar o usuário
                const index = usuariosDemostracao.findIndex(u => u.id === parseInt(id));
                
                if (index === -1) {
                    reject({
                        success: false,
                        message: 'Usuário não encontrado.'
                    });
                    return;
                }
                
                // Verificar se é o próprio usuário
                const usuarioLogado = obterUsuarioLogado();
                if (usuarioLogado && usuarioLogado.id === parseInt(id)) {
                    reject({
                        success: false,
                        message: 'Você não pode excluir seu próprio usuário.'
                    });
                    return;
                }
                
                // Excluir usuário
                usuariosDemostracao.splice(index, 1);
                
                resolve({
                    success: true,
                    message: 'Usuário excluído com sucesso!'
                });
            } catch (error) {
                reject({
                    success: false,
                    message: `Erro ao excluir usuário: ${error.message}`
                });
            }
        }, 500); // Simular um pequeno atraso
    });
}

/**
 * Envia um link de redefinição de senha para o email do usuário
 * @param {number} id - ID do usuário
 * @param {string} email - Email do usuário
 * @returns {Promise} - Promise com o resultado do envio
 */
function enviarLinkRedefinicaoSenha(id, email) {
    return new Promise((resolve, reject) => {
        // Verificar se o usuário logado é administrador
        if (!eAdmin()) {
            reject({
                success: false,
                message: 'Você não tem permissão para redefinir senhas.'
            });
            return;
        }
        
        // Simular uma chamada assíncrona
        setTimeout(() => {
            try {
                // Encontrar o usuário
                const usuario = usuariosDemostracao.find(u => u.id === parseInt(id));
                
                if (!usuario) {
                    reject({
                        success: false,
                        message: 'Usuário não encontrado.'
                    });
                    return;
                }
                
                // Verificar se o email corresponde
                if (usuario.email !== email) {
                    reject({
                        success: false,
                        message: 'O email não corresponde ao usuário selecionado.'
                    });
                    return;
                }
                
                // Simular envio de email
                console.log(`Link de redefinição enviado para ${email}`);
                
                resolve({
                    success: true,
                    message: `Link de redefinição de senha enviado para ${email}`
                });
            } catch (error) {
                reject({
                    success: false,
                    message: `Erro ao enviar link de redefinição: ${error.message}`
                });
            }
        }, 1000); // Simular um pequeno atraso
    });
}
