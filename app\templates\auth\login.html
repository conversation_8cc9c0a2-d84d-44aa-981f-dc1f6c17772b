{% extends 'base.html' %}

{% block title %}Login - CRM Advocacia{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>Login</h4>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.login') }}">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {% if form.email.errors %}
                            {{ form.email(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.email(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.senha.label(class="form-label") }}
                        {% if form.senha.errors %}
                            {{ form.senha(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.senha.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.senha(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="mb-3 form-check">
                        {{ form.lembrar_me(class="form-check-input") }}
                        {{ form.lembrar_me.label(class="form-check-label") }}
                    </div>
                    
                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">Não tem uma conta? <a href="{{ url_for('auth.registro') }}">Registre-se</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
