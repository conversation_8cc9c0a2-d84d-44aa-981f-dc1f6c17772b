{% extends 'base.html' %}

{% block title %}Registro - CRM Advocacia{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-user-plus me-2"></i>Registro de Novo Usuário</h4>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('auth.registro') }}">
                    {{ form.hidden_tag() }}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            {{ form.nome.label(class="form-label") }}
                            {% if form.nome.errors %}
                                {{ form.nome(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.nome.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.nome(class="form-control") }}
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {{ form.email.label(class="form-label") }}
                            {% if form.email.errors %}
                                {{ form.email(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.email(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            {{ form.oab.label(class="form-label") }}
                            {% if form.oab.errors %}
                                {{ form.oab(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.oab.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.oab(class="form-control") }}
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {{ form.cargo.label(class="form-label") }}
                            {% if form.cargo.errors %}
                                {{ form.cargo(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.cargo.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.cargo(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.telefone.label(class="form-label") }}
                        {% if form.telefone.errors %}
                            {{ form.telefone(class="form-control is-invalid") }}
                            <div class="invalid-feedback">
                                {% for error in form.telefone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% else %}
                            {{ form.telefone(class="form-control") }}
                        {% endif %}
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            {{ form.senha.label(class="form-label") }}
                            {% if form.senha.errors %}
                                {{ form.senha(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.senha.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.senha(class="form-control") }}
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            {{ form.confirmar_senha.label(class="form-label") }}
                            {% if form.confirmar_senha.errors %}
                                {{ form.confirmar_senha(class="form-control is-invalid") }}
                                <div class="invalid-feedback">
                                    {% for error in form.confirmar_senha.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.confirmar_senha(class="form-control") }}
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">Já tem uma conta? <a href="{{ url_for('auth.login') }}">Faça login</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
