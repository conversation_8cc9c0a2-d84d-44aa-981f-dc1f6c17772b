{% extends 'base.html' %}

{% block title %}Dashboard - CRM Advocacia{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
        <p class="text-muted">Bem-vindo, {{ current_user.nome }}. Aqui está um resumo das suas atividades.</p>
    </div>
</div>

<!-- Cards de Estatísticas -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Processos</h5>
                        <h2 class="mb-0">{{ total_processos }}</h2>
                    </div>
                    <i class="fas fa-gavel fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <span>Ver todos</span>
                <a href="{{ url_for('processos.listar') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Clientes</h5>
                        <h2 class="mb-0">{{ total_clientes }}</h2>
                    </div>
                    <i class="fas fa-users fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <span>Ver todos</span>
                <a href="{{ url_for('clientes.listar') }}" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Prazos Próximos</h5>
                        <h2 class="mb-0">{{ prazos_proximos|length }}</h2>
                    </div>
                    <i class="fas fa-calendar-alt fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <span>Ver todos</span>
                <a href="#prazos-proximos" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Processos por Status -->
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Processos por Status</h5>
            </div>
            <div class="card-body">
                {% if processos_por_status %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Quantidade</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for status, count in processos_por_status %}
                            <tr>
                                <td>{{ status }}</td>
                                <td>{{ count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">Nenhum processo cadastrado.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Prazos Próximos -->
    <div class="col-md-6 mb-4" id="prazos-proximos">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Prazos Próximos</h5>
            </div>
            <div class="card-body">
                {% if prazos_proximos %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Prazo</th>
                                <th>Processo</th>
                                <th>Data Limite</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for prazo in prazos_proximos %}
                            <tr>
                                <td>{{ prazo.titulo }}</td>
                                <td>
                                    <a href="{{ url_for('processos.visualizar', id=prazo.processo.id) }}">
                                        {{ prazo.processo.numero }}
                                    </a>
                                </td>
                                <td>{{ prazo.data_fim.strftime('%d/%m/%Y') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">Nenhum prazo próximo.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Processos Recentes -->
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-gavel me-2"></i>Processos Recentes</h5>
            </div>
            <div class="card-body">
                {% if processos_recentes %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Número</th>
                                <th>Título</th>
                                <th>Cliente</th>
                                <th>Status</th>
                                <th>Data Cadastro</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for processo in processos_recentes %}
                            <tr>
                                <td>{{ processo.numero }}</td>
                                <td>{{ processo.titulo }}</td>
                                <td>{{ processo.cliente.nome }}</td>
                                <td>
                                    <span class="badge {% if processo.status == 'Em andamento' %}bg-primary{% elif processo.status == 'Concluído' %}bg-success{% elif processo.status == 'Arquivado' %}bg-secondary{% elif processo.status == 'Suspenso' %}bg-warning{% else %}bg-info{% endif %}">
                                        {{ processo.status }}
                                    </span>
                                </td>
                                <td>{{ processo.data_cadastro.strftime('%d/%m/%Y') }}</td>
                                <td>
                                    <a href="{{ url_for('processos.visualizar', id=processo.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">Nenhum processo cadastrado.</p>
                {% endif %}
            </div>
            <div class="card-footer text-end">
                <a href="{{ url_for('processos.listar') }}" class="btn btn-primary btn-sm">
                    Ver Todos <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Aqui podemos adicionar código para gráficos usando Chart.js
    // Exemplo: gráfico de processos por status
    document.addEventListener('DOMContentLoaded', function() {
        // Implementar gráficos quando necessário
    });
</script>
{% endblock %}
