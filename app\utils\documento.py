import os
import json
from docxtpl import DocxTemplate
from datetime import datetime
from flask import current_app
from werkzeug.utils import secure_filename

def gerar_documento_a_partir_de_modelo(modelo_path, dados, nome_arquivo=None):
    """
    Gera um documento a partir de um modelo DOCX usando a biblioteca docxtpl.
    
    Args:
        modelo_path (str): Caminho para o arquivo de modelo DOCX
        dados (dict): Dicionário com os dados para preencher o modelo
        nome_arquivo (str, optional): Nome do arquivo gerado. Se None, será gerado automaticamente.
        
    Returns:
        str: Caminho para o arquivo gerado
    """
    try:
        # Carregar o modelo
        doc = DocxTemplate(modelo_path)
        
        # Adicionar data atual aos dados
        dados['data_atual'] = datetime.now().strftime('%d/%m/%Y')
        
        # Renderizar o documento com os dados
        doc.render(dados)
        
        # Gerar nome do arquivo se não fornecido
        if nome_arquivo is None:
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            nome_arquivo = f"documento_gerado_{timestamp}.docx"
        else:
            nome_arquivo = secure_filename(nome_arquivo)
            if not nome_arquivo.endswith('.docx'):
                nome_arquivo += '.docx'
        
        # Caminho para salvar o arquivo gerado
        output_path = os.path.join(current_app.config['UPLOAD_FOLDER'], nome_arquivo)
        
        # Salvar o documento gerado
        doc.save(output_path)
        
        return nome_arquivo
    
    except Exception as e:
        print(f"Erro ao gerar documento: {str(e)}")
        return None

def extrair_campos_modelo(modelo_path):
    """
    Extrai os campos disponíveis em um modelo DOCX.
    
    Args:
        modelo_path (str): Caminho para o arquivo de modelo DOCX
        
    Returns:
        list: Lista de campos encontrados no modelo
    """
    try:
        # Carregar o modelo
        doc = DocxTemplate(modelo_path)
        
        # Obter campos do modelo
        campos = doc.get_undeclared_template_variables()
        
        return list(campos)
    
    except Exception as e:
        print(f"Erro ao extrair campos do modelo: {str(e)}")
        return []
