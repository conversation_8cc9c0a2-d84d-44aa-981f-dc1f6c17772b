import os
from datetime import timedelta

class Config:
    # Configuração básica
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'chave-secreta-temporaria'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///crm_advocacia.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Configuração de upload de arquivos
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app', 'static', 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max upload
    
    # Configuração de sessão
    PERMANENT_SESSION_LIFETIME = timedelta(days=1)
    
    # Configuração de e-mail (para notificações)
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS') or True
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # Cores do tema (baseadas em escritórios de advocacia tradicionais)
    THEME_COLORS = {
        'primary': '#0A3161',  # Azul marinho
        'secondary': '#D4AF37',  # Dourado
        'accent': '#4A4A4A',  # Cinza escuro
        'text': '#333333',  # Quase preto
        'background': '#F5F5F5',  # Cinza claro
    }

class DevelopmentConfig(Config):
    DEBUG = True
    
class ProductionConfig(Config):
    DEBUG = False
    # Em produção, use uma chave secreta forte
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'chave-secreta-forte-para-producao'
    
    # Em produção, considere usar PostgreSQL
    # SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'postgresql://user:password@localhost/crm_advocacia'

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
