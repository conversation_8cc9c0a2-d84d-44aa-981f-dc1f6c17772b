import os
import shutil

# Caminhos de origem e destino
imagens_dir = r"C:\Users\<USER>\Desktop\PROJETOS BROBOT\PROJETO 4\imagens"
static_img_dir = "static/img"
static_video_dir = "static/video"

# Criar diretórios de destino se não existirem
os.makedirs(static_img_dir, exist_ok=True)
os.makedirs(static_video_dir, exist_ok=True)

# Copiar arquivos
try:
    # Listar arquivos na pasta de imagens
    print(f"Verificando arquivos em: {imagens_dir}")
    files = os.listdir(imagens_dir)
    print(f"Arquivos encontrados: {files}")
    
    # Copiar arquivos para as pastas corretas
    for file in files:
        file_path = os.path.join(imagens_dir, file)
        if os.path.isfile(file_path):
            # Verificar se é um arquivo de imagem
            if file.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.svg')):
                # Verificar se é a logo branca
                if "LOGO BRANCA" in file.upper():
                    dest_path = os.path.join(static_img_dir, "LOGO BRANCA.png")
                    shutil.copy2(file_path, dest_path)
                    print(f"Copiado: {file} -> {dest_path}")
                else:
                    dest_path = os.path.join(static_img_dir, file)
                    shutil.copy2(file_path, dest_path)
                    print(f"Copiado: {file} -> {dest_path}")
            
            # Verificar se é um arquivo de vídeo
            elif file.lower().endswith(('.mp4', '.webm', '.ogg')):
                dest_path = os.path.join(static_video_dir, "Videodefundo.mp4")
                shutil.copy2(file_path, dest_path)
                print(f"Copiado: {file} -> {dest_path}")
    
    print("Cópia de arquivos concluída com sucesso!")
except Exception as e:
    print(f"Erro ao copiar arquivos: {e}")
