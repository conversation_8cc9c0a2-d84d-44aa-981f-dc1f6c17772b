import os
import shutil
import glob

# Caminhos
imagens_dir = r"C:\Users\<USER>\Desktop\PROJETOS BROBOT\PROJETO 4\imagens"
static_video_dir = "static/video"

# Criar diretório de destino se não existir
os.makedirs(static_video_dir, exist_ok=True)

# Procurar por arquivos de vídeo na pasta de imagens
video_files = []
for ext in ['*.mp4', '*.webm', '*.mov', '*.avi']:
    video_files.extend(glob.glob(os.path.join(imagens_dir, ext)))

if video_files:
    # Copiar o primeiro vídeo encontrado
    video_file = video_files[0]
    dest_path = os.path.join(static_video_dir, "Videodefundo.mp4")
    
    try:
        shutil.copy2(video_file, dest_path)
        print(f"Vídeo copiado com sucesso: {video_file} -> {dest_path}")
    except Exception as e:
        print(f"Erro ao copiar o vídeo: {e}")
else:
    print("Nenhum arquivo de vídeo encontrado na pasta de imagens.")
