# Configuração do Banco de Dados - CRM Advocacia

Este diretório contém os scripts necessários para configurar o banco de dados MySQL para o CRM Advocacia.

## Requisitos

- MySQL Server 5.7 ou superior
- PHP 7.4 ou superior com extensão PDO habilitada

## Arquivos

- `schema.sql`: Define a estrutura do banco de dados (tabelas, índices, etc.)
- `seed.sql`: Insere dados iniciais no banco de dados
- `init_db.php`: Script PHP para inicializar o banco de dados
- `db_config.php`: Configurações de conexão com o banco de dados
- `test_connection.php`: Script para testar a conexão com o banco de dados

## Configuração

1. Instale o MySQL Server em sua máquina, se ainda não estiver instalado.

2. Verifique as configurações de conexão no arquivo `db_config.php`:
   ```php
   $db_config = [
       'host' => 'localhost',
       'username' => 'root',
       'password' => '',
       'database' => 'crm_advocacia',
       'charset' => 'utf8mb4',
       'port' => 3306
   ];
   ```

3. Ajuste as configurações conforme necessário para seu ambiente.

## Inicialização do Banco de Dados

### Método 1: Usando o script PHP

Execute o script `init_db.php` para criar o banco de dados, as tabelas e inserir os dados iniciais:

```bash
php init_db.php
```

### Método 2: Usando o MySQL diretamente

1. Crie o banco de dados:
   ```sql
   CREATE DATABASE crm_advocacia CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. Execute o script `schema.sql`:
   ```bash
   mysql -u root -p crm_advocacia < schema.sql
   ```

3. Execute o script `seed.sql`:
   ```bash
   mysql -u root -p crm_advocacia < seed.sql
   ```

## Testando a Conexão

Execute o script `test_connection.php` para verificar se a conexão com o banco de dados está funcionando corretamente:

```bash
php test_connection.php
```

## Credenciais de Acesso

Após a inicialização do banco de dados, você pode acessar o sistema com as seguintes credenciais:

### Administrador
- Email: <EMAIL>
- Senha: admin123

### Advogado
- Email: <EMAIL>
- Senha: advogado123

## Estrutura do Banco de Dados

O banco de dados contém as seguintes tabelas:

- `usuarios`: Armazena informações dos usuários (advogados e administradores)
- `clientes`: Armazena informações dos clientes
- `processos`: Armazena informações dos processos judiciais
- `andamentos`: Armazena os andamentos dos processos
- `prazos`: Armazena os prazos processuais
- `documentos`: Armazena informações sobre os documentos
- `modelos_documentos`: Armazena modelos de documentos para geração automática

## Solução de Problemas

### Erro de conexão com o banco de dados

Se você receber um erro de conexão, verifique:

1. Se o servidor MySQL está em execução
2. Se as credenciais no arquivo `db_config.php` estão corretas
3. Se o banco de dados `crm_advocacia` existe

### Erro ao executar os scripts SQL

Se você receber erros ao executar os scripts SQL, verifique:

1. Se você tem permissões suficientes no MySQL
2. Se o banco de dados `crm_advocacia` existe
3. Se não há tabelas com o mesmo nome já existentes
