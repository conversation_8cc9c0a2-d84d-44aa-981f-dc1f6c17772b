<?php
// Configurações do banco de dados
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'crm_advocacia',
    'charset' => 'utf8mb4',
    'port' => 3306
];

// Função para conectar ao banco de dados
function conectarBD() {
    global $db_config;
    
    try {
        $dsn = "mysql:host={$db_config['host']};dbname={$db_config['database']};charset={$db_config['charset']};port={$db_config['port']}";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        
        $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], $options);
        return $pdo;
    } catch (PDOException $e) {
        // Em produção, você deve registrar o erro em um log em vez de exibi-lo
        die("Erro de conexão com o banco de dados: " . $e->getMessage());
    }
}
