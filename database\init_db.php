<?php
// Configurações do banco de dados
$host = 'localhost';
$username = 'root';
$password = '';
$port = 3306;

try {
    // Conectar ao servidor MySQL sem selecionar um banco de dados
    $pdo = new PDO("mysql:host=$host;port=$port", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Conectado ao servidor MySQL com sucesso!\n";
    
    // Ler e executar o arquivo schema.sql
    $schema = file_get_contents(__DIR__ . '/schema.sql');
    $pdo->exec($schema);
    
    echo "Esquema do banco de dados criado com sucesso!\n";
    
    // Ler e executar o arquivo seed.sql
    $seed = file_get_contents(__DIR__ . '/seed.sql');
    $pdo->exec($seed);
    
    echo "Dados iniciais inseridos com sucesso!\n";
    
    echo "Banco de dados inicializado com sucesso!\n";
    
    // Exibir informações de acesso
    echo "\n";
    echo "Informações de acesso:\n";
    echo "---------------------\n";
    echo "Administrador:\n";
    echo "  Email: <EMAIL>\n";
    echo "  Senha: admin123\n";
    echo "\n";
    echo "Advogado:\n";
    echo "  Email: <EMAIL>\n";
    echo "  Senha: advogado123\n";
    echo "\n";
    
} catch (PDOException $e) {
    die("Erro ao inicializar o banco de dados: " . $e->getMessage() . "\n");
}
