-- Criação do banco de dados
CREATE DATABASE IF NOT EXISTS crm_advocacia CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Usar o banco de dados
USE crm_advocacia;

-- <PERSON><PERSON><PERSON>uários (Advogados e Administradores)
CREATE TABLE IF NOT EXISTS usuarios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    senha_hash VARCHAR(255) NOT NULL,
    oab VARCHAR(20) UNIQUE,
    cargo VARCHAR(50),
    telefone VARCHAR(20),
    foto_perfil VARCHAR(255),
    data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
    ultimo_acesso DATETIME,
    ativo BOOLEAN DEFAULT TRUE,
    admin BOOLEAN DEFAULT FALSE,
    token_redefinicao VARCHAR(255),
    expiracao_token DATETIME
);

-- <PERSON><PERSON><PERSON>
CREATE TABLE IF NOT EXISTS clientes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    cpf_cnpj VARCHAR(20) NOT NULL UNIQUE,
    tipo ENUM('PF', 'PJ') NOT NULL,
    email VARCHAR(100),
    telefone VARCHAR(20),
    celular VARCHAR(20),
    endereco VARCHAR(255),
    cidade VARCHAR(100),
    estado CHAR(2),
    cep VARCHAR(10),
    data_nascimento DATE,
    observacoes TEXT,
    data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP,
    ultima_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ativo BOOLEAN DEFAULT TRUE,
    advogado_id INT NOT NULL,
    FOREIGN KEY (advogado_id) REFERENCES usuarios(id)
);

-- Tabela de Processos
CREATE TABLE IF NOT EXISTS processos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero VARCHAR(50) NOT NULL UNIQUE,
    titulo VARCHAR(255) NOT NULL,
    descricao TEXT,
    tipo VARCHAR(50),
    area VARCHAR(50),
    vara VARCHAR(100),
    comarca VARCHAR(100),
    tribunal VARCHAR(100),
    valor_causa DECIMAL(15, 2),
    status VARCHAR(50),
    data_inicio DATE,
    data_conclusao DATE,
    data_cadastro DATETIME DEFAULT CURRENT_TIMESTAMP,
    ultima_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    cliente_id INT NOT NULL,
    advogado_id INT NOT NULL,
    FOREIGN KEY (cliente_id) REFERENCES clientes(id),
    FOREIGN KEY (advogado_id) REFERENCES usuarios(id)
);

-- Tabela de Andamentos
CREATE TABLE IF NOT EXISTS andamentos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    data DATETIME NOT NULL,
    descricao TEXT NOT NULL,
    tipo VARCHAR(50),
    observacoes TEXT,
    processo_id INT NOT NULL,
    FOREIGN KEY (processo_id) REFERENCES processos(id) ON DELETE CASCADE
);

-- Tabela de Prazos
CREATE TABLE IF NOT EXISTS prazos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titulo VARCHAR(255) NOT NULL,
    descricao TEXT,
    data_inicio DATETIME NOT NULL,
    data_fim DATETIME NOT NULL,
    concluido BOOLEAN DEFAULT FALSE,
    prioridade VARCHAR(20),
    processo_id INT NOT NULL,
    FOREIGN KEY (processo_id) REFERENCES processos(id) ON DELETE CASCADE
);

-- Tabela de Documentos
CREATE TABLE IF NOT EXISTS documentos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    tipo VARCHAR(50),
    descricao TEXT,
    caminho_arquivo VARCHAR(255) NOT NULL,
    data_upload DATETIME DEFAULT CURRENT_TIMESTAMP,
    tamanho INT,
    formato VARCHAR(10),
    processo_id INT,
    cliente_id INT,
    FOREIGN KEY (processo_id) REFERENCES processos(id) ON DELETE CASCADE,
    FOREIGN KEY (cliente_id) REFERENCES clientes(id) ON DELETE CASCADE
);

-- Tabela de Modelos de Documentos
CREATE TABLE IF NOT EXISTS modelos_documentos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nome VARCHAR(255) NOT NULL,
    descricao TEXT,
    tipo VARCHAR(50) NOT NULL,
    caminho_arquivo VARCHAR(255) NOT NULL,
    data_criacao DATETIME DEFAULT CURRENT_TIMESTAMP,
    ultima_atualizacao DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    ativo BOOLEAN DEFAULT TRUE,
    campos_disponiveis TEXT
);

-- Índices para melhorar a performance
CREATE INDEX idx_processos_cliente ON processos(cliente_id);
CREATE INDEX idx_processos_advogado ON processos(advogado_id);
CREATE INDEX idx_clientes_advogado ON clientes(advogado_id);
CREATE INDEX idx_andamentos_processo ON andamentos(processo_id);
CREATE INDEX idx_prazos_processo ON prazos(processo_id);
CREATE INDEX idx_documentos_processo ON documentos(processo_id);
CREATE INDEX idx_documentos_cliente ON documentos(cliente_id);
