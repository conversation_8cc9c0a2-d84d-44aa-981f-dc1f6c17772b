-- Usar o banco de dados
USE crm_advocacia;

-- Inserir usuários (senhas: 'admin123', 'advogado123', 'maria123', 'carlos123')
-- As senhas estão em formato hash usando bcrypt
INSERT INTO usuarios (nome, email, senha_hash, oab, cargo, telefone, ativo, admin) VALUES
('Administrador', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '123456', 'Administrador', '(11) 99999-9999', TRUE, TRUE),
('<PERSON>', '<EMAIL>', '$2y$10$hP7VzxC6G0iKCKGAmpOQUeOBBCJqL2Ixf6V3DWQRZhKSqGi1A3kQi', '654321', 'Advogado', '(11) 88888-8888', TRUE, FALSE),
('<PERSON>', '<EMAIL>', '$2y$10$3i9/lVd8UOFIJ6PAMFt8gu3/r5g0bZgbZo19g7tFl43EypYQaZ2Ua', '789012', 'Advogada', '(11) 77777-7777', TRUE, FALSE),
('Carlos Pereira', '<EMAIL>', '$2y$10$gKkjY.xbpz3UVemx8DLxAO4RYj0yzOXQs.9NeGonMcjbLVMZjjdCu', '345678', 'Advogado Sênior', '(11) 66666-6666', FALSE, FALSE);

-- Inserir clientes
INSERT INTO clientes (nome, cpf_cnpj, tipo, email, telefone, celular, endereco, cidade, estado, cep, observacoes, advogado_id) VALUES
('Empresa ABC Ltda', '12.345.678/0001-90', 'PJ', '<EMAIL>', '(11) 3333-4444', '(11) 99999-8888', 'Av. Paulista, 1000', 'São Paulo', 'SP', '01310-100', 'Cliente desde 2020', 1),
('Maria Oliveira', '123.456.789-00', 'PF', '<EMAIL>', NULL, '(11) 97777-6666', 'Rua das Flores, 123', 'São Paulo', 'SP', '04567-000', 'Cliente indicada pelo Dr. Carlos', 2),
('João Pereira', '987.654.321-00', 'PF', '<EMAIL>', NULL, '(11) 96666-5555', 'Av. Brasil, 500', 'Rio de Janeiro', 'RJ', '20000-000', NULL, 2),
('Empresa XYZ S.A.', '98.765.432/0001-10', 'PJ', '<EMAIL>', '(11) 2222-3333', '(11) 95555-4444', 'Rua do Comércio, 789', 'São Paulo', 'SP', '01500-000', 'Empresa multinacional', 3);

-- Inserir processos
INSERT INTO processos (numero, titulo, descricao, tipo, area, vara, comarca, tribunal, valor_causa, status, data_inicio, cliente_id, advogado_id) VALUES
('1234567-12.2023.8.26.0100', 'Ação de Cobrança', 'Ação de cobrança contra Empresa XYZ', 'Civil', 'Contratos', '5ª Vara Cível', 'São Paulo', 'TJSP', 50000.00, 'Em andamento', '2023-01-15', 1, 1),
('9876543-98.2023.8.26.0100', 'Ação Trabalhista', 'Reclamação trabalhista contra Empresa ABC', 'Trabalhista', 'Direito do Trabalho', '10ª Vara do Trabalho', 'São Paulo', 'TRT-2', 30000.00, 'Em andamento', '2023-02-20', 2, 2),
('5555555-55.2023.8.26.0100', 'Ação de Indenização', 'Indenização por danos morais', 'Civil', 'Responsabilidade Civil', '3ª Vara Cível', 'São Paulo', 'TJSP', 75000.00, 'Concluído', '2023-03-10', 3, 2),
('7777777-77.2023.8.26.0100', 'Execução Fiscal', 'Execução de tributos municipais', 'Tributário', 'Execução Fiscal', '2ª Vara de Fazenda Pública', 'São Paulo', 'TJSP', 120000.00, 'Em andamento', '2023-04-05', 4, 3);

-- Inserir andamentos
INSERT INTO andamentos (data, descricao, tipo, processo_id) VALUES
('2023-01-20 10:00:00', 'Petição inicial protocolada', 'Petição', 1),
('2023-02-10 14:30:00', 'Citação realizada', 'Citação', 1),
('2023-02-25 09:15:00', 'Petição inicial protocolada', 'Petição', 2),
('2023-03-15 11:00:00', 'Audiência de conciliação realizada', 'Audiência', 2),
('2023-03-10 10:30:00', 'Petição inicial protocolada', 'Petição', 3),
('2023-04-20 15:45:00', 'Sentença favorável proferida', 'Sentença', 3);

-- Inserir prazos
INSERT INTO prazos (titulo, descricao, data_inicio, data_fim, concluido, prioridade, processo_id) VALUES
('Contestação', 'Prazo para apresentar contestação', NOW(), DATE_ADD(NOW(), INTERVAL 15 DAY), FALSE, 'Alta', 1),
('Audiência Inicial', 'Audiência de conciliação', DATE_ADD(NOW(), INTERVAL 5 DAY), DATE_ADD(NOW(), INTERVAL 5 DAY), FALSE, 'Alta', 2),
('Recurso', 'Prazo para apresentar recurso', NOW(), DATE_ADD(NOW(), INTERVAL 10 DAY), FALSE, 'Média', 3),
('Manifestação', 'Prazo para manifestação sobre documentos', NOW(), DATE_ADD(NOW(), INTERVAL 7 DAY), FALSE, 'Baixa', 4);

-- Inserir documentos (caminhos de arquivo fictícios)
INSERT INTO documentos (nome, tipo, descricao, caminho_arquivo, tamanho, formato, processo_id) VALUES
('Petição Inicial', 'Petição', 'Petição inicial do processo', 'uploads/processo_1/peticao_inicial.pdf', 1024000, 'PDF', 1),
('Procuração', 'Procuração', 'Procuração do cliente', 'uploads/processo_1/procuracao.pdf', 512000, 'PDF', 1),
('Petição Inicial', 'Petição', 'Petição inicial do processo', 'uploads/processo_2/peticao_inicial.pdf', 1536000, 'PDF', 2),
('Ata de Audiência', 'Ata', 'Ata da audiência de conciliação', 'uploads/processo_2/ata_audiencia.pdf', 768000, 'PDF', 2);

-- Inserir documentos de clientes
INSERT INTO documentos (nome, tipo, descricao, caminho_arquivo, tamanho, formato, cliente_id) VALUES
('Contrato Social', 'Documento', 'Contrato social da empresa', 'uploads/cliente_1/contrato_social.pdf', 2048000, 'PDF', 1),
('RG e CPF', 'Documento', 'Documentos pessoais', 'uploads/cliente_2/rg_cpf.pdf', 1024000, 'PDF', 2),
('Comprovante de Residência', 'Documento', 'Comprovante de residência', 'uploads/cliente_3/comprovante_residencia.pdf', 512000, 'PDF', 3);

-- Inserir modelos de documentos
INSERT INTO modelos_documentos (nome, descricao, tipo, caminho_arquivo, campos_disponiveis) VALUES
('Petição Inicial - Ação de Cobrança', 'Modelo de petição inicial para ações de cobrança', 'Petição Inicial', 'modelos/peticao_inicial_cobranca.docx', '{"cliente_nome", "cliente_cpf_cnpj", "valor_causa", "data_vencimento"}'),
('Procuração', 'Modelo de procuração padrão', 'Procuração', 'modelos/procuracao.docx', '{"cliente_nome", "cliente_cpf_cnpj", "advogado_nome", "advogado_oab"}'),
('Contestação', 'Modelo de contestação genérica', 'Contestação', 'modelos/contestacao.docx', '{"processo_numero", "cliente_nome", "parte_contraria", "data_citacao"}');
