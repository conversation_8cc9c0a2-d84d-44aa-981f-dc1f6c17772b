<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM Advocacia - Demo</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        /* Cores do tema para escritório de advocacia */
        :root {
            --primary-color: #0A3161;    /* Azul marinho */
            --secondary-color: #D4AF37;  /* Dourado */
            --accent-color: #4A4A4A;     /* Cinza escuro */
            --text-color: #333333;       /* Quase preto */
            --bg-color: #F5F5F5;         /* Cinza claro */
            --light-color: #FFFFFF;      /* Branco */
            --danger-color: #DC3545;     /* Vermelho */
            --success-color: #28A745;    /* Verde */
            --warning-color: #FFC107;    /* Amarelo */
            --info-color: #17A2B8;       /* Azul claro */
        }

        body {
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
        }

        /* Navbar personalizada */
        .navbar-dark.bg-primary {
            background-color: var(--primary-color) !important;
        }

        .navbar-brand {
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            border-radius: 8px 8px 0 0 !important;
            font-weight: 600;
        }

        /* Botões */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: #082a54;
            border-color: #082a54;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: var(--text-color);
        }

        .btn-secondary:hover, .btn-secondary:focus {
            background-color: #c09c30;
            border-color: #c09c30;
            color: var(--text-color);
        }

        /* Footer */
        .footer {
            background-color: var(--primary-color) !important;
            color: var(--light-color);
            padding: 1rem 0;
            margin-top: 2rem;
        }

        .footer .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        /* Toast personalizado */
        .toast {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            opacity: 1 !important;
            border-radius: 10px;
            overflow: hidden;
            animation: slideIn 0.3s ease-in-out;
        }

        @keyframes slideIn {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .toast-header {
            border-bottom: none;
            padding: 0.75rem 1rem;
        }

        .toast-header .btn-close {
            margin-right: -0.375rem;
        }

        .toast-body {
            font-size: 1.1rem;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-balance-scale me-2"></i>CRM Advocacia
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-gavel me-1"></i>Processos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-users me-1"></i>Clientes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-file-alt me-1"></i>Documentos
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <!-- Botão de Administração - Visível apenas para administradores -->
                    <li class="nav-item dropdown me-2 d-none">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>Administração
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#novoUsuarioModal">
                                    <i class="fas fa-user-plus me-1"></i>Novo Usuário
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="gerenciar-usuarios.html">
                                    <i class="fas fa-users-cog me-1"></i>Gerenciar Usuários
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-database me-1"></i>Configurações do Sistema
                                </a>
                            </li>
                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>Administrador
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-id-card me-1"></i>Perfil
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="login.html">
                                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
                <p class="text-muted">Bem-vindo, Administrador. Aqui está um resumo das suas atividades.</p>
            </div>
        </div>

        <!-- Cards de Estatísticas -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title">Processos</h5>
                                <h2 class="mb-0">12</h2>
                            </div>
                            <i class="fas fa-gavel fa-3x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-between align-items-center">
                        <span>Ver todos</span>
                        <a href="#" class="text-white">
                            <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title">Clientes</h5>
                                <h2 class="mb-0">8</h2>
                            </div>
                            <i class="fas fa-users fa-3x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-between align-items-center">
                        <span>Ver todos</span>
                        <a href="#" class="text-white">
                            <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="card-title">Prazos Próximos</h5>
                                <h2 class="mb-0">5</h2>
                            </div>
                            <i class="fas fa-calendar-alt fa-3x opacity-50"></i>
                        </div>
                    </div>
                    <div class="card-footer d-flex justify-content-between align-items-center">
                        <span>Ver todos</span>
                        <a href="#" class="text-white">
                            <i class="fas fa-arrow-circle-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Processos por Status -->
            <div class="col-md-6 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Processos por Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Status</th>
                                        <th>Quantidade</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Em andamento</td>
                                        <td>7</td>
                                    </tr>
                                    <tr>
                                        <td>Concluído</td>
                                        <td>3</td>
                                    </tr>
                                    <tr>
                                        <td>Arquivado</td>
                                        <td>1</td>
                                    </tr>
                                    <tr>
                                        <td>Suspenso</td>
                                        <td>1</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prazos Próximos -->
            <div class="col-md-6 mb-4" id="prazos-proximos">
                <div class="card shadow">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Prazos Próximos</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Prazo</th>
                                        <th>Processo</th>
                                        <th>Data Limite</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Contestação</td>
                                        <td>1234567-12.2023.8.26.0100</td>
                                        <td>15/12/2023</td>
                                    </tr>
                                    <tr>
                                        <td>Audiência Inicial</td>
                                        <td>9876543-98.2023.8.26.0100</td>
                                        <td>18/12/2023</td>
                                    </tr>
                                    <tr>
                                        <td>Recurso</td>
                                        <td>5555555-55.2023.8.26.0100</td>
                                        <td>20/12/2023</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Processos Recentes -->
        <div class="row">
            <div class="col-md-12 mb-4">
                <div class="card shadow">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-gavel me-2"></i>Processos Recentes</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Número</th>
                                        <th>Título</th>
                                        <th>Cliente</th>
                                        <th>Status</th>
                                        <th>Data Cadastro</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1234567-12.2023.8.26.0100</td>
                                        <td>Ação de Cobrança</td>
                                        <td>Empresa ABC Ltda</td>
                                        <td>
                                            <span class="badge bg-primary">Em andamento</span>
                                        </td>
                                        <td>15/01/2023</td>
                                        <td>
                                            <a href="#" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>9876543-98.2023.8.26.0100</td>
                                        <td>Ação Trabalhista</td>
                                        <td>Maria Silva</td>
                                        <td>
                                            <span class="badge bg-primary">Em andamento</span>
                                        </td>
                                        <td>20/02/2023</td>
                                        <td>
                                            <a href="#" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>5555555-55.2023.8.26.0100</td>
                                        <td>Ação de Indenização</td>
                                        <td>João Pereira</td>
                                        <td>
                                            <span class="badge bg-success">Concluído</span>
                                        </td>
                                        <td>10/03/2023</td>
                                        <td>
                                            <a href="#" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="card-footer text-end">
                        <a href="#" class="btn btn-primary btn-sm">
                            Ver Todos <i class="fas fa-arrow-right ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer py-3">
        <div class="container text-center">
            <span class="text-muted">© 2023 CRM Advocacia. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Toast de Boas-vindas -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
        <div id="welcomeToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header" style="background-color: var(--secondary-color); color: var(--text-color);">
                <i class="fas fa-user-check me-2"></i>
                <strong class="me-auto">Bem-vindo!</strong>
                <small>Agora</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Fechar"></button>
            </div>
            <div class="toast-body">
                <span id="welcomeMessage">Bem-vindo ao CRM Advocacia!</span>
            </div>
        </div>
    </div>

    <!-- Modal de Novo Usuário -->
    <div class="modal fade" id="novoUsuarioModal" tabindex="-1" aria-labelledby="novoUsuarioModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="novoUsuarioModalLabel">
                        <i class="fas fa-user-plus me-2"></i>Cadastrar Novo Usuário
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="novoUsuarioForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="nome" class="form-label">Nome Completo</label>
                                <input type="text" class="form-control" id="nome" name="nome" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="oab" class="form-label">Número OAB</label>
                                <input type="text" class="form-control" id="oab" name="oab" required>
                            </div>
                            <div class="col-md-6">
                                <label for="cargo" class="form-label">Cargo</label>
                                <input type="text" class="form-control" id="cargo" name="cargo">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="telefone" class="form-label">Telefone</label>
                                <input type="text" class="form-control" id="telefone" name="telefone">
                            </div>
                            <div class="col-md-6">
                                <label for="tipo" class="form-label">Tipo de Usuário</label>
                                <select class="form-select" id="tipo" name="tipo" required>
                                    <option value="advogado">Advogado</option>
                                    <option value="admin">Administrador</option>
                                    <option value="assistente">Assistente</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="senha" class="form-label">Senha</label>
                                <input type="password" class="form-control" id="senha" name="senha" required>
                            </div>
                            <div class="col-md-6">
                                <label for="confirmarSenha" class="form-label">Confirmar Senha</label>
                                <input type="password" class="form-control" id="confirmarSenha" name="confirmarSenha" required>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="ativo" name="ativo" checked>
                            <label class="form-check-label" for="ativo">Usuário Ativo</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="btnSalvarUsuario">
                        <i class="fas fa-save me-1"></i>Salvar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Script de autenticação -->
    <script src="app/static/js/auth.js"></script>

    <script>
        // Verificar se o usuário está logado
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar se o usuário está logado
            if (!estaLogado()) {
                window.location.href = 'login.html';
                return;
            }

            // Obter dados do usuário logado
            const usuario = obterUsuarioLogado();

            // Atualizar nome do usuário na barra de navegação
            const userDropdown = document.getElementById('userDropdown');
            if (userDropdown && usuario) {
                userDropdown.innerHTML = `<i class="fas fa-user-circle me-1"></i>${usuario.nome}`;
            }

            // Mostrar toast de boas-vindas se for o primeiro acesso após login
            if (sessionStorage.getItem('showWelcome') === 'true') {
                // Personalizar a mensagem com o nome do usuário
                const welcomeMessage = document.getElementById('welcomeMessage');
                if (welcomeMessage && usuario) {
                    welcomeMessage.textContent = `Bem-vindo, ${usuario.nome}!`;
                }

                // Mostrar o toast
                const welcomeToast = new bootstrap.Toast(document.getElementById('welcomeToast'), {
                    delay: 5000 // Mostrar por 5 segundos
                });
                welcomeToast.show();

                // Remover a flag para não mostrar novamente ao atualizar a página
                sessionStorage.removeItem('showWelcome');
            }

            // Mostrar/ocultar menu de administração
            const adminDropdown = document.getElementById('adminDropdown');
            if (adminDropdown) {
                if (usuario && usuario.admin) {
                    adminDropdown.parentElement.classList.remove('d-none');
                } else {
                    adminDropdown.parentElement.classList.add('d-none');
                }
            }

            // Configurar botão de logout
            const logoutButton = document.querySelector('a[href="login.html"]');
            if (logoutButton) {
                logoutButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    realizarLogout();
                });
            }

            // Script para validação do formulário de novo usuário
            const btnSalvarUsuario = document.getElementById('btnSalvarUsuario');
            const novoUsuarioForm = document.getElementById('novoUsuarioForm');
            const novoUsuarioModal = document.getElementById('novoUsuarioModal');

            if (btnSalvarUsuario && novoUsuarioForm && novoUsuarioModal) {
                btnSalvarUsuario.addEventListener('click', function() {
                    // Verificar se o formulário é válido
                    if (novoUsuarioForm.checkValidity()) {
                        // Verificar se as senhas coincidem
                        const senha = document.getElementById('senha').value;
                        const confirmarSenha = document.getElementById('confirmarSenha').value;

                        if (senha !== confirmarSenha) {
                            alert('As senhas não coincidem!');
                            return;
                        }

                        // Obter dados do formulário
                        const dadosUsuario = {
                            nome: document.getElementById('nome').value,
                            email: document.getElementById('email').value,
                            oab: document.getElementById('oab').value,
                            cargo: document.getElementById('cargo').value,
                            telefone: document.getElementById('telefone').value,
                            tipo: document.getElementById('tipo').value,
                            senha: senha,
                            ativo: document.getElementById('ativo').checked
                        };

                        // Desabilitar o botão durante o processo
                        const originalButtonText = btnSalvarUsuario.innerHTML;
                        btnSalvarUsuario.disabled = true;
                        btnSalvarUsuario.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Salvando...';

                        // Criar usuário
                        criarUsuario(dadosUsuario)
                            .then(result => {
                                // Usuário criado com sucesso
                                alert(result.message);

                                // Fechar o modal
                                const modal = bootstrap.Modal.getInstance(novoUsuarioModal);
                                modal.hide();

                                // Limpar o formulário
                                novoUsuarioForm.reset();
                            })
                            .catch(error => {
                                // Erro ao criar usuário
                                alert(error.message);
                            })
                            .finally(() => {
                                // Restaurar o botão
                                btnSalvarUsuario.disabled = false;
                                btnSalvarUsuario.innerHTML = originalButtonText;
                            });
                    } else {
                        // Forçar a validação do formulário
                        novoUsuarioForm.reportValidity();
                    }
                });
            }
        });
    </script>
</body>
</html>
