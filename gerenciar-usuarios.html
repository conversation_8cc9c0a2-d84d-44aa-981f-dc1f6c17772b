<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Usuários - CRM Advocacia</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    
    <style>
        /* Cores do tema para escritório de advocacia */
        :root {
            --primary-color: #0A3161;    /* Azul marinho */
            --secondary-color: #D4AF37;  /* Dourado */
            --accent-color: #4A4A4A;     /* Cinza escuro */
            --text-color: #333333;       /* Quase preto */
            --bg-color: #F5F5F5;         /* Cinza claro */
            --light-color: #FFFFFF;      /* Branco */
            --danger-color: #DC3545;     /* Vermelho */
            --success-color: #28A745;    /* Verde */
            --warning-color: #FFC107;    /* Amarelo */
            --info-color: #17A2B8;       /* Azul claro */
        }

        body {
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
        }

        /* Navbar personalizada */
        .navbar-dark.bg-primary {
            background-color: var(--primary-color) !important;
        }

        .navbar-brand {
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            border-radius: 8px 8px 0 0 !important;
            font-weight: 600;
        }

        /* Botões */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: #082a54;
            border-color: #082a54;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: var(--text-color);
        }

        .btn-secondary:hover, .btn-secondary:focus {
            background-color: #c09c30;
            border-color: #c09c30;
            color: var(--text-color);
        }

        /* Footer */
        .footer {
            background-color: var(--primary-color) !important;
            color: var(--light-color);
            padding: 1rem 0;
            margin-top: 2rem;
        }

        .footer .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
        }
        
        /* Status de usuário */
        .status-ativo {
            color: var(--success-color);
        }
        
        .status-inativo {
            color: var(--danger-color);
        }
        
        /* DataTables personalização */
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: var(--primary-color) !important;
            color: white !important;
            border-color: var(--primary-color) !important;
        }
        
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #082a54 !important;
            color: white !important;
            border-color: #082a54 !important;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="demo.html">
                <i class="fas fa-balance-scale me-2"></i>CRM Advocacia
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="demo.html">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-gavel me-1"></i>Processos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-users me-1"></i>Clientes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-file-alt me-1"></i>Documentos
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <!-- Botão de Administração - Visível apenas para administradores -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link dropdown-toggle active" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>Administração
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#novoUsuarioModal">
                                    <i class="fas fa-user-plus me-1"></i>Novo Usuário
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item active" href="gerenciar-usuarios.html">
                                    <i class="fas fa-users-cog me-1"></i>Gerenciar Usuários
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-database me-1"></i>Configurações do Sistema
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>Administrador
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-id-card me-1"></i>Perfil
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="login.html">
                                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2><i class="fas fa-users-cog me-2"></i>Gerenciar Usuários</h2>
                <p class="text-muted">Gerencie os usuários do sistema, edite informações e redefina senhas.</p>
            </div>
        </div>

        <!-- Lista de Usuários -->
        <div class="row">
            <div class="col-md-12">
                <div class="card shadow">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>Lista de Usuários</h5>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#novoUsuarioModal">
                            <i class="fas fa-user-plus me-1"></i>Novo Usuário
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="tabelaUsuarios" class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Nome</th>
                                        <th>Email</th>
                                        <th>OAB</th>
                                        <th>Cargo</th>
                                        <th>Telefone</th>
                                        <th>Tipo</th>
                                        <th>Status</th>
                                        <th>Último Acesso</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Os dados serão carregados via JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer py-3">
        <div class="container text-center">
            <span class="text-muted">© 2023 CRM Advocacia. Todos os direitos reservados.</span>
        </div>
    </footer>
    
    <!-- Modal de Novo Usuário -->
    <div class="modal fade" id="novoUsuarioModal" tabindex="-1" aria-labelledby="novoUsuarioModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="novoUsuarioModalLabel">
                        <i class="fas fa-user-plus me-2"></i>Cadastrar Novo Usuário
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="novoUsuarioForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="nome" class="form-label">Nome Completo</label>
                                <input type="text" class="form-control" id="nome" name="nome" required>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="oab" class="form-label">Número OAB</label>
                                <input type="text" class="form-control" id="oab" name="oab" required>
                            </div>
                            <div class="col-md-6">
                                <label for="cargo" class="form-label">Cargo</label>
                                <input type="text" class="form-control" id="cargo" name="cargo">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="telefone" class="form-label">Telefone</label>
                                <input type="text" class="form-control" id="telefone" name="telefone">
                            </div>
                            <div class="col-md-6">
                                <label for="tipo" class="form-label">Tipo de Usuário</label>
                                <select class="form-select" id="tipo" name="tipo" required>
                                    <option value="advogado">Advogado</option>
                                    <option value="admin">Administrador</option>
                                    <option value="assistente">Assistente</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="senha" class="form-label">Senha</label>
                                <input type="password" class="form-control" id="senha" name="senha" required>
                            </div>
                            <div class="col-md-6">
                                <label for="confirmarSenha" class="form-label">Confirmar Senha</label>
                                <input type="password" class="form-control" id="confirmarSenha" name="confirmarSenha" required>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="ativo" name="ativo" checked>
                            <label class="form-check-label" for="ativo">Usuário Ativo</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="btnSalvarUsuario">
                        <i class="fas fa-save me-1"></i>Salvar
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal de Editar Usuário -->
    <div class="modal fade" id="editarUsuarioModal" tabindex="-1" aria-labelledby="editarUsuarioModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editarUsuarioModalLabel">
                        <i class="fas fa-user-edit me-2"></i>Editar Usuário
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <form id="editarUsuarioForm">
                        <input type="hidden" id="editarId" name="editarId">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="editarNome" class="form-label">Nome Completo</label>
                                <input type="text" class="form-control" id="editarNome" name="editarNome" required>
                            </div>
                            <div class="col-md-6">
                                <label for="editarEmail" class="form-label">Email</label>
                                <input type="email" class="form-control" id="editarEmail" name="editarEmail" required>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="editarOab" class="form-label">Número OAB</label>
                                <input type="text" class="form-control" id="editarOab" name="editarOab" required>
                            </div>
                            <div class="col-md-6">
                                <label for="editarCargo" class="form-label">Cargo</label>
                                <input type="text" class="form-control" id="editarCargo" name="editarCargo">
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="editarTelefone" class="form-label">Telefone</label>
                                <input type="text" class="form-control" id="editarTelefone" name="editarTelefone">
                            </div>
                            <div class="col-md-6">
                                <label for="editarTipo" class="form-label">Tipo de Usuário</label>
                                <select class="form-select" id="editarTipo" name="editarTipo" required>
                                    <option value="advogado">Advogado</option>
                                    <option value="admin">Administrador</option>
                                    <option value="assistente">Assistente</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="editarAtivo" name="editarAtivo">
                            <label class="form-check-label" for="editarAtivo">Usuário Ativo</label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="btnAtualizarUsuario">
                        <i class="fas fa-save me-1"></i>Salvar Alterações
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal de Redefinir Senha -->
    <div class="modal fade" id="redefinirSenhaModal" tabindex="-1" aria-labelledby="redefinirSenhaModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="redefinirSenhaModalLabel">
                        <i class="fas fa-key me-2"></i>Redefinir Senha
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
                </div>
                <div class="modal-body">
                    <p>Um link para redefinição de senha será enviado para o email do usuário.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>O link de redefinição será válido por 24 horas.
                    </div>
                    <form id="redefinirSenhaForm">
                        <input type="hidden" id="redefinirId" name="redefinirId">
                        <div class="mb-3">
                            <label for="redefinirEmail" class="form-label">Email do Usuário</label>
                            <input type="email" class="form-control" id="redefinirEmail" name="redefinirEmail" readonly>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="btnEnviarRedefinicao">
                        <i class="fas fa-paper-plane me-1"></i>Enviar Link
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Script de autenticação -->
    <script src="app/static/js/auth.js"></script>
    
    <!-- Script de gerenciamento de usuários -->
    <script src="app/static/js/usuarios.js"></script>
</body>
</html>
