<?php
// Página inicial do CRM Advocacia
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM Advocacia</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Cores do tema para escritório de advocacia */
        :root {
            --primary-color: #0A3161;    /* Azul marinho */
            --secondary-color: #D4AF37;  /* Dourado */
            --accent-color: #4A4A4A;     /* Cinza escuro */
            --text-color: #333333;       /* Quase preto */
            --bg-color: #F5F5F5;         /* Cinza claro */
            --light-color: #FFFFFF;      /* Branco */
        }

        body {
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            max-width: 800px;
            width: 100%;
        }

        .card-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 10px 10px 0 0 !important;
            padding: 1.5rem;
        }

        .card-title {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .card-body {
            padding: 2rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: #082a54;
            border-color: #082a54;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: var(--text-color);
        }

        .btn-secondary:hover, .btn-secondary:focus {
            background-color: #c09c30;
            border-color: #c09c30;
            color: var(--text-color);
        }

        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 1rem 0;
            margin-top: auto;
        }

        .footer .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .icon-large {
            font-size: 4rem;
            color: var(--secondary-color);
            margin-bottom: 1rem;
        }

        .feature-icon {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="main-content">
        <div class="card">
            <div class="card-header text-center">
                <i class="fas fa-balance-scale icon-large"></i>
                <h1 class="card-title">CRM Advocacia</h1>
                <p class="card-subtitle">Sistema de Gerenciamento para Escritórios de Advocacia</p>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <p class="lead">Bem-vindo ao CRM Advocacia! Escolha uma das opções abaixo para começar:</p>
                </div>
                
                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-sign-in-alt feature-icon"></i>
                                <h5 class="card-title">Acessar o Sistema</h5>
                                <p class="card-text">Faça login para acessar o dashboard e todas as funcionalidades do sistema.</p>
                                <a href="login.html" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>Entrar
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-database feature-icon"></i>
                                <h5 class="card-title">Visualizar Banco de Dados</h5>
                                <p class="card-text">Veja os dados armazenados no sistema (demonstração).</p>
                                <a href="visualizar-banco-dados.html" class="btn btn-secondary">
                                    <i class="fas fa-database me-2"></i>Ver Dados
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row g-4 mt-2">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-users-cog feature-icon"></i>
                                <h5 class="card-title">Gerenciar Usuários</h5>
                                <p class="card-text">Acesse o painel de gerenciamento de usuários (apenas para administradores).</p>
                                <a href="gerenciar-usuarios.html" class="btn btn-primary">
                                    <i class="fas fa-users-cog me-2"></i>Gerenciar
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-tachometer-alt feature-icon"></i>
                                <h5 class="card-title">Dashboard</h5>
                                <p class="card-text">Acesse diretamente o dashboard principal do sistema.</p>
                                <a href="demo.html" class="btn btn-secondary">
                                    <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Nota:</strong> Este é um sistema de demonstração. Para uma implementação completa, é necessário configurar o PHP e o MySQL.
                </div>
            </div>
        </div>
    </div>
    
    <footer class="footer">
        <div class="container">
            <span class="text-muted">© 2023 CRM Advocacia. Todos os direitos reservados.</span>
        </div>
    </footer>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
