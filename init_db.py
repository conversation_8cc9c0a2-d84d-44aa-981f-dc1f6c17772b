from app import create_app, db
from app.models.usuario import Usuario
from app.models.cliente import Cliente
from app.models.processo import Processo, Andamento, Prazo
from app.models.documento import Documento, ModeloDocumento
from datetime import datetime, timedelta
import os

def init_db():
    """Inicializa o banco de dados com dados de exemplo"""
    app = create_app('development')
    
    with app.app_context():
        # Criar todas as tabelas
        db.create_all()
        
        # Verificar se já existem usuários
        if Usuario.query.count() == 0:
            print("Criando usuário administrador...")
            
            # Criar usuário administrador
            admin = Usuario(
                nome="Administrador",
                email="<EMAIL>",
                oab="123456",
                cargo="Administrador",
                telefone="(11) 99999-9999",
                admin=True,
                ativo=True
            )
            admin.senha = "admin123"
            
            # Criar usuário advogado
            advogado = Usuario(
                nome="<PERSON> Advogado",
                email="<EMAIL>",
                oab="654321",
                cargo="Advogado",
                telefone="(11) 88888-8888",
                admin=False,
                ativo=True
            )
            advogado.senha = "advogado123"
            
            db.session.add(admin)
            db.session.add(advogado)
            db.session.commit()
            
            print("Usuários criados com sucesso!")
            print("Admin: <EMAIL> / admin123")
            print("Advogado: <EMAIL> / advogado123")
            
            # Criar clientes de exemplo
            print("Criando clientes de exemplo...")
            
            cliente1 = Cliente(
                nome="Empresa ABC Ltda",
                cpf_cnpj="12.345.678/0001-90",
                tipo="PJ",
                email="<EMAIL>",
                telefone="(11) 3333-4444",
                celular="(11) 99999-8888",
                endereco="Av. Paulista, 1000",
                cidade="São Paulo",
                estado="SP",
                cep="01310-100",
                observacoes="Cliente desde 2020",
                advogado_id=admin.id
            )
            
            cliente2 = Cliente(
                nome="Maria Silva",
                cpf_cnpj="123.456.789-00",
                tipo="PF",
                email="<EMAIL>",
                telefone="",
                celular="(11) 97777-6666",
                endereco="Rua das Flores, 123",
                cidade="São Paulo",
                estado="SP",
                cep="04567-000",
                data_nascimento=datetime(1985, 5, 15),
                observacoes="Cliente indicada pelo Dr. Carlos",
                advogado_id=advogado.id
            )
            
            db.session.add(cliente1)
            db.session.add(cliente2)
            db.session.commit()
            
            print("Clientes criados com sucesso!")
            
            # Criar processos de exemplo
            print("Criando processos de exemplo...")
            
            processo1 = Processo(
                numero="1234567-12.2023.8.26.0100",
                titulo="Ação de Cobrança",
                descricao="Ação de cobrança contra Empresa XYZ",
                tipo="Civil",
                area="Contratos",
                vara="5ª Vara Cível",
                comarca="São Paulo",
                tribunal="TJSP",
                valor_causa=50000.00,
                status="Em andamento",
                data_inicio=datetime(2023, 1, 15),
                cliente_id=cliente1.id,
                advogado_id=admin.id
            )
            
            processo2 = Processo(
                numero="9876543-98.2023.8.26.0100",
                titulo="Ação Trabalhista",
                descricao="Reclamação trabalhista contra Empresa ABC",
                tipo="Trabalhista",
                area="Direito do Trabalho",
                vara="10ª Vara do Trabalho",
                comarca="São Paulo",
                tribunal="TRT-2",
                valor_causa=30000.00,
                status="Em andamento",
                data_inicio=datetime(2023, 2, 20),
                cliente_id=cliente2.id,
                advogado_id=advogado.id
            )
            
            db.session.add(processo1)
            db.session.add(processo2)
            db.session.commit()
            
            print("Processos criados com sucesso!")
            
            # Criar andamentos
            andamento1 = Andamento(
                data=datetime(2023, 1, 20),
                descricao="Petição inicial protocolada",
                tipo="Petição",
                processo_id=processo1.id
            )
            
            andamento2 = Andamento(
                data=datetime(2023, 2, 10),
                descricao="Citação realizada",
                tipo="Citação",
                processo_id=processo1.id
            )
            
            andamento3 = Andamento(
                data=datetime(2023, 2, 25),
                descricao="Petição inicial protocolada",
                tipo="Petição",
                processo_id=processo2.id
            )
            
            db.session.add(andamento1)
            db.session.add(andamento2)
            db.session.add(andamento3)
            db.session.commit()
            
            # Criar prazos
            hoje = datetime.now().date()
            
            prazo1 = Prazo(
                titulo="Contestação",
                descricao="Prazo para apresentar contestação",
                data_inicio=hoje,
                data_fim=hoje + timedelta(days=15),
                prioridade="Alta",
                processo_id=processo1.id
            )
            
            prazo2 = Prazo(
                titulo="Audiência Inicial",
                descricao="Audiência de conciliação",
                data_inicio=hoje + timedelta(days=5),
                data_fim=hoje + timedelta(days=5),
                prioridade="Alta",
                processo_id=processo2.id
            )
            
            db.session.add(prazo1)
            db.session.add(prazo2)
            db.session.commit()
            
            print("Andamentos e prazos criados com sucesso!")
            
            print("Banco de dados inicializado com sucesso!")
        else:
            print("O banco de dados já contém dados. Nenhuma alteração foi feita.")

if __name__ == "__main__":
    init_db()
