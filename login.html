<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - CRM Advocacia</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        /* Cores do tema para escritório de advocacia */
        :root {
            --primary-color: #0A3161;    /* Azul marinho */
            --secondary-color: #D4AF37;  /* Dourado */
            --accent-color: #4A4A4A;     /* Cinza escuro */
            --text-color: #333333;       /* Quase preto */
            --bg-color: #F5F5F5;         /* Cinza claro */
            --light-color: #FFFFFF;      /* Branco */
        }

        body {
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .login-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-card {
            width: 100%;
            max-width: 450px;
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .login-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 10px 10px 0 0;
            padding: 20px;
            text-align: center;
        }

        .login-logo {
            margin-bottom: 15px;
        }

        .login-logo i {
            font-size: 3rem;
            color: var(--secondary-color);
        }

        .login-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
        }

        .login-subtitle {
            font-size: 1rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        .login-body {
            padding: 30px;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(10, 49, 97, 0.25);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: #082a54;
            border-color: #082a54;
        }

        .login-footer {
            text-align: center;
            padding: 15px;
            border-top: 1px solid #eee;
        }

        .footer {
            background-color: var(--primary-color);
            color: white;
            text-align: center;
            padding: 15px 0;
            margin-top: auto;
        }

        .footer .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .alert {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="card login-card">
            <div class="login-header">
                <div class="login-logo">
                    <i class="fas fa-balance-scale"></i>
                </div>
                <h1 class="login-title">CRM Advocacia</h1>
                <p class="login-subtitle">Sistema de Gerenciamento para Escritórios de Advocacia</p>
            </div>

            <div class="login-body">
                <!-- Mensagem de erro (inicialmente oculta) -->
                <div class="alert alert-danger d-none" id="error-message">
                    Usuário ou senha inválidos. Por favor, tente novamente.
                </div>

                <form id="login-form">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label for="password" class="form-label">Senha</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" placeholder="Sua senha" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember-me" name="remember-me">
                        <label class="form-check-label" for="remember-me">Lembrar-me</label>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-sign-in-alt me-2"></i>Entrar
                        </button>
                    </div>
                </form>
            </div>

            <div class="login-footer">
                <p class="mb-0">© 2023 CRM Advocacia. Todos os direitos reservados.</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Script de autenticação -->
    <script src="app/static/js/auth.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar se o usuário já está logado
            if (estaLogado()) {
                window.location.href = 'demo.html';
                return;
            }

            // Alternar visibilidade da senha
            const togglePassword = document.getElementById('toggle-password');
            const passwordInput = document.getElementById('password');

            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });

            // Processar o formulário de login
            const loginForm = document.getElementById('login-form');
            const errorMessage = document.getElementById('error-message');

            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const email = document.getElementById('email').value;
                const password = document.getElementById('password').value;
                const rememberMe = document.getElementById('remember-me').checked;

                // Desabilitar o botão de login durante o processo
                const submitButton = loginForm.querySelector('button[type="submit"]');
                const originalButtonText = submitButton.innerHTML;
                submitButton.disabled = true;
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Entrando...';

                // Tentar fazer login
                realizarLogin(email, password, rememberMe)
                    .then(result => {
                        // Armazenar informação de primeiro login para mostrar mensagem de boas-vindas
                        sessionStorage.setItem('showWelcome', 'true');

                        // Login bem-sucedido - redirecionar para o dashboard
                        window.location.href = 'demo.html';
                    })
                    .catch(error => {
                        // Login falhou - mostrar mensagem de erro
                        errorMessage.textContent = error.message;
                        errorMessage.classList.remove('d-none');

                        // Restaurar o botão
                        submitButton.disabled = false;
                        submitButton.innerHTML = originalButtonText;

                        // Esconder a mensagem após 3 segundos
                        setTimeout(() => {
                            errorMessage.classList.add('d-none');
                        }, 3000);
                    });
            });
        });
    </script>
</body>
</html>
