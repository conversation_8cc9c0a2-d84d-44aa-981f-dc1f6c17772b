<?php
require_once __DIR__ . '/../database/db_config.php';

class Cliente {
    private $pdo;
    
    public function __construct() {
        $this->pdo = conectarBD();
    }
    
    /**
     * Busca um cliente pelo ID
     */
    public function buscarPorId($id) {
        $stmt = $this->pdo->prepare("SELECT * FROM clientes WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    /**
     * Lista todos os clientes de um advogado
     */
    public function listarPorAdvogado($advogado_id) {
        $stmt = $this->pdo->prepare("SELECT * FROM clientes WHERE advogado_id = ? ORDER BY nome");
        $stmt->execute([$advogado_id]);
        return $stmt->fetchAll();
    }
    
    /**
     * Lista todos os clientes
     */
    public function listarTodos() {
        $stmt = $this->pdo->query("SELECT c.*, u.nome as advogado_nome FROM clientes c JOIN usuarios u ON c.advogado_id = u.id ORDER BY c.nome");
        return $stmt->fetchAll();
    }
    
    /**
     * Cria um novo cliente
     */
    public function criar($nome, $cpf_cnpj, $tipo, $email, $telefone, $celular, $endereco, $cidade, $estado, $cep, $data_nascimento, $observacoes, $advogado_id) {
        $stmt = $this->pdo->prepare("INSERT INTO clientes (nome, cpf_cnpj, tipo, email, telefone, celular, endereco, cidade, estado, cep, data_nascimento, observacoes, advogado_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$nome, $cpf_cnpj, $tipo, $email, $telefone, $celular, $endereco, $cidade, $estado, $cep, $data_nascimento, $observacoes, $advogado_id]);
        
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Atualiza um cliente existente
     */
    public function atualizar($id, $nome, $cpf_cnpj, $tipo, $email, $telefone, $celular, $endereco, $cidade, $estado, $cep, $data_nascimento, $observacoes, $ativo) {
        $stmt = $this->pdo->prepare("UPDATE clientes SET nome = ?, cpf_cnpj = ?, tipo = ?, email = ?, telefone = ?, celular = ?, endereco = ?, cidade = ?, estado = ?, cep = ?, data_nascimento = ?, observacoes = ?, ativo = ? WHERE id = ?");
        return $stmt->execute([$nome, $cpf_cnpj, $tipo, $email, $telefone, $celular, $endereco, $cidade, $estado, $cep, $data_nascimento, $observacoes, $ativo, $id]);
    }
    
    /**
     * Exclui um cliente
     */
    public function excluir($id) {
        $stmt = $this->pdo->prepare("DELETE FROM clientes WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * Busca clientes por termo de pesquisa
     */
    public function buscar($termo, $advogado_id = null) {
        $sql = "SELECT * FROM clientes WHERE (nome LIKE ? OR cpf_cnpj LIKE ? OR email LIKE ?)";
        $params = ["%$termo%", "%$termo%", "%$termo%"];
        
        if ($advogado_id) {
            $sql .= " AND advogado_id = ?";
            $params[] = $advogado_id;
        }
        
        $sql .= " ORDER BY nome";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
}
