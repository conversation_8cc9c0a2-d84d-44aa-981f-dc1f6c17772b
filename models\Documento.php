<?php
require_once __DIR__ . '/../database/db_config.php';

class Documento {
    private $pdo;
    
    public function __construct() {
        $this->pdo = conectarBD();
    }
    
    /**
     * Busca um documento pelo ID
     */
    public function buscarPorId($id) {
        $stmt = $this->pdo->prepare("SELECT * FROM documentos WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    /**
     * Lista documentos de um processo
     */
    public function listarPorProcesso($processo_id) {
        $stmt = $this->pdo->prepare("SELECT * FROM documentos WHERE processo_id = ? ORDER BY data_upload DESC");
        $stmt->execute([$processo_id]);
        return $stmt->fetchAll();
    }
    
    /**
     * Lista documentos de um cliente
     */
    public function listarPorCliente($cliente_id) {
        $stmt = $this->pdo->prepare("SELECT * FROM documentos WHERE cliente_id = ? ORDER BY data_upload DESC");
        $stmt->execute([$cliente_id]);
        return $stmt->fetchAll();
    }
    
    /**
     * Lista documentos de um advogado (através dos processos)
     */
    public function listarPorAdvogado($advogado_id) {
        $stmt = $this->pdo->prepare("SELECT d.*, p.numero as processo_numero, p.titulo as processo_titulo, c.nome as cliente_nome 
                                    FROM documentos d 
                                    LEFT JOIN processos p ON d.processo_id = p.id 
                                    LEFT JOIN clientes c ON (d.cliente_id = c.id OR p.cliente_id = c.id) 
                                    WHERE p.advogado_id = ? OR c.advogado_id = ? 
                                    ORDER BY d.data_upload DESC");
        $stmt->execute([$advogado_id, $advogado_id]);
        return $stmt->fetchAll();
    }
    
    /**
     * Adiciona um novo documento
     */
    public function adicionar($nome, $tipo, $descricao, $caminho_arquivo, $tamanho, $formato, $processo_id = null, $cliente_id = null) {
        $stmt = $this->pdo->prepare("INSERT INTO documentos (nome, tipo, descricao, caminho_arquivo, tamanho, formato, processo_id, cliente_id) 
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$nome, $tipo, $descricao, $caminho_arquivo, $tamanho, $formato, $processo_id, $cliente_id]);
        
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Atualiza um documento existente
     */
    public function atualizar($id, $nome, $tipo, $descricao) {
        $stmt = $this->pdo->prepare("UPDATE documentos SET nome = ?, tipo = ?, descricao = ? WHERE id = ?");
        return $stmt->execute([$nome, $tipo, $descricao, $id]);
    }
    
    /**
     * Exclui um documento
     */
    public function excluir($id) {
        $stmt = $this->pdo->prepare("DELETE FROM documentos WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * Busca documentos por termo de pesquisa
     */
    public function buscar($termo, $advogado_id = null) {
        $sql = "SELECT d.*, p.numero as processo_numero, p.titulo as processo_titulo, c.nome as cliente_nome 
                FROM documentos d 
                LEFT JOIN processos p ON d.processo_id = p.id 
                LEFT JOIN clientes c ON (d.cliente_id = c.id OR p.cliente_id = c.id) 
                WHERE (d.nome LIKE ? OR d.tipo LIKE ? OR d.descricao LIKE ?)";
        $params = ["%$termo%", "%$termo%", "%$termo%"];
        
        if ($advogado_id) {
            $sql .= " AND (p.advogado_id = ? OR c.advogado_id = ?)";
            $params[] = $advogado_id;
            $params[] = $advogado_id;
        }
        
        $sql .= " ORDER BY d.data_upload DESC";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Lista modelos de documentos
     */
    public function listarModelos() {
        $stmt = $this->pdo->query("SELECT * FROM modelos_documentos WHERE ativo = TRUE ORDER BY nome");
        return $stmt->fetchAll();
    }
    
    /**
     * Busca um modelo de documento pelo ID
     */
    public function buscarModeloPorId($id) {
        $stmt = $this->pdo->prepare("SELECT * FROM modelos_documentos WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    /**
     * Adiciona um novo modelo de documento
     */
    public function adicionarModelo($nome, $descricao, $tipo, $caminho_arquivo, $campos_disponiveis) {
        $stmt = $this->pdo->prepare("INSERT INTO modelos_documentos (nome, descricao, tipo, caminho_arquivo, campos_disponiveis) 
                                    VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$nome, $descricao, $tipo, $caminho_arquivo, $campos_disponiveis]);
        
        return $this->pdo->lastInsertId();
    }
}
