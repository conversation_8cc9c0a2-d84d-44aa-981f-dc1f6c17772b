<?php
require_once __DIR__ . '/../database/db_config.php';

class Processo {
    private $pdo;
    
    public function __construct() {
        $this->pdo = conectarBD();
    }
    
    /**
     * Busca um processo pelo ID
     */
    public function buscarPorId($id) {
        $stmt = $this->pdo->prepare("SELECT p.*, c.nome as cliente_nome, u.nome as advogado_nome 
                                     FROM processos p 
                                     JOIN clientes c ON p.cliente_id = c.id 
                                     JOIN usuarios u ON p.advogado_id = u.id 
                                     WHERE p.id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    /**
     * Lista todos os processos de um advogado
     */
    public function listarPorAdvogado($advogado_id) {
        $stmt = $this->pdo->prepare("SELECT p.*, c.nome as cliente_nome 
                                     FROM processos p 
                                     JOIN clientes c ON p.cliente_id = c.id 
                                     WHERE p.advogado_id = ? 
                                     ORDER BY p.data_cadastro DESC");
        $stmt->execute([$advogado_id]);
        return $stmt->fetchAll();
    }
    
    /**
     * Lista todos os processos de um cliente
     */
    public function listarPorCliente($cliente_id) {
        $stmt = $this->pdo->prepare("SELECT p.*, u.nome as advogado_nome 
                                     FROM processos p 
                                     JOIN usuarios u ON p.advogado_id = u.id 
                                     WHERE p.cliente_id = ? 
                                     ORDER BY p.data_cadastro DESC");
        $stmt->execute([$cliente_id]);
        return $stmt->fetchAll();
    }
    
    /**
     * Lista todos os processos
     */
    public function listarTodos() {
        $stmt = $this->pdo->query("SELECT p.*, c.nome as cliente_nome, u.nome as advogado_nome 
                                  FROM processos p 
                                  JOIN clientes c ON p.cliente_id = c.id 
                                  JOIN usuarios u ON p.advogado_id = u.id 
                                  ORDER BY p.data_cadastro DESC");
        return $stmt->fetchAll();
    }
    
    /**
     * Cria um novo processo
     */
    public function criar($numero, $titulo, $descricao, $tipo, $area, $vara, $comarca, $tribunal, $valor_causa, $status, $data_inicio, $cliente_id, $advogado_id) {
        $stmt = $this->pdo->prepare("INSERT INTO processos (numero, titulo, descricao, tipo, area, vara, comarca, tribunal, valor_causa, status, data_inicio, cliente_id, advogado_id) 
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$numero, $titulo, $descricao, $tipo, $area, $vara, $comarca, $tribunal, $valor_causa, $status, $data_inicio, $cliente_id, $advogado_id]);
        
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Atualiza um processo existente
     */
    public function atualizar($id, $numero, $titulo, $descricao, $tipo, $area, $vara, $comarca, $tribunal, $valor_causa, $status, $data_inicio, $data_conclusao, $cliente_id) {
        $stmt = $this->pdo->prepare("UPDATE processos SET numero = ?, titulo = ?, descricao = ?, tipo = ?, area = ?, vara = ?, comarca = ?, tribunal = ?, valor_causa = ?, status = ?, data_inicio = ?, data_conclusao = ?, cliente_id = ? WHERE id = ?");
        return $stmt->execute([$numero, $titulo, $descricao, $tipo, $area, $vara, $comarca, $tribunal, $valor_causa, $status, $data_inicio, $data_conclusao, $cliente_id, $id]);
    }
    
    /**
     * Exclui um processo
     */
    public function excluir($id) {
        $stmt = $this->pdo->prepare("DELETE FROM processos WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * Busca processos por termo de pesquisa
     */
    public function buscar($termo, $advogado_id = null) {
        $sql = "SELECT p.*, c.nome as cliente_nome 
                FROM processos p 
                JOIN clientes c ON p.cliente_id = c.id 
                WHERE (p.numero LIKE ? OR p.titulo LIKE ? OR p.status LIKE ? OR c.nome LIKE ?)";
        $params = ["%$termo%", "%$termo%", "%$termo%", "%$termo%"];
        
        if ($advogado_id) {
            $sql .= " AND p.advogado_id = ?";
            $params[] = $advogado_id;
        }
        
        $sql .= " ORDER BY p.data_cadastro DESC";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Conta processos por status
     */
    public function contarPorStatus($advogado_id = null) {
        $sql = "SELECT status, COUNT(*) as total FROM processos";
        $params = [];
        
        if ($advogado_id) {
            $sql .= " WHERE advogado_id = ?";
            $params[] = $advogado_id;
        }
        
        $sql .= " GROUP BY status";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Obtém os andamentos de um processo
     */
    public function obterAndamentos($processo_id) {
        $stmt = $this->pdo->prepare("SELECT * FROM andamentos WHERE processo_id = ? ORDER BY data DESC");
        $stmt->execute([$processo_id]);
        return $stmt->fetchAll();
    }
    
    /**
     * Adiciona um andamento a um processo
     */
    public function adicionarAndamento($processo_id, $data, $descricao, $tipo, $observacoes = null) {
        $stmt = $this->pdo->prepare("INSERT INTO andamentos (processo_id, data, descricao, tipo, observacoes) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$processo_id, $data, $descricao, $tipo, $observacoes]);
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Obtém os prazos de um processo
     */
    public function obterPrazos($processo_id) {
        $stmt = $this->pdo->prepare("SELECT * FROM prazos WHERE processo_id = ? ORDER BY data_fim");
        $stmt->execute([$processo_id]);
        return $stmt->fetchAll();
    }
    
    /**
     * Adiciona um prazo a um processo
     */
    public function adicionarPrazo($processo_id, $titulo, $descricao, $data_inicio, $data_fim, $prioridade) {
        $stmt = $this->pdo->prepare("INSERT INTO prazos (processo_id, titulo, descricao, data_inicio, data_fim, prioridade) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([$processo_id, $titulo, $descricao, $data_inicio, $data_fim, $prioridade]);
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Marca um prazo como concluído
     */
    public function concluirPrazo($prazo_id) {
        $stmt = $this->pdo->prepare("UPDATE prazos SET concluido = TRUE WHERE id = ?");
        return $stmt->execute([$prazo_id]);
    }
    
    /**
     * Obtém prazos próximos (não concluídos)
     */
    public function obterPrazosProximos($advogado_id, $dias = 7) {
        $data_limite = date('Y-m-d', strtotime("+$dias days"));
        
        $stmt = $this->pdo->prepare("SELECT pr.*, p.numero as processo_numero, p.titulo as processo_titulo 
                                    FROM prazos pr 
                                    JOIN processos p ON pr.processo_id = p.id 
                                    WHERE p.advogado_id = ? 
                                    AND pr.concluido = FALSE 
                                    AND pr.data_fim <= ? 
                                    ORDER BY pr.data_fim");
        $stmt->execute([$advogado_id, $data_limite]);
        return $stmt->fetchAll();
    }
}
