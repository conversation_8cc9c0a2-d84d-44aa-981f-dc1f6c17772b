<?php
require_once __DIR__ . '/../database/db_config.php';

class Usuario {
    private $pdo;
    
    public function __construct() {
        $this->pdo = conectarBD();
    }
    
    /**
     * Busca um usuário pelo ID
     */
    public function buscarPorId($id) {
        $stmt = $this->pdo->prepare("SELECT id, nome, email, oab, cargo, telefone, foto_perfil, data_criacao, ultimo_acesso, ativo, admin FROM usuarios WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    /**
     * Busca um usuário pelo email
     */
    public function buscarPorEmail($email) {
        $stmt = $this->pdo->prepare("SELECT id, nome, email, senha_hash, oab, cargo, telefone, foto_perfil, data_criacao, ultimo_acesso, ativo, admin FROM usuarios WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetch();
    }
    
    /**
     * Lista todos os usuários
     */
    public function listarTodos() {
        $stmt = $this->pdo->query("SELECT id, nome, email, oab, cargo, telefone, foto_perfil, data_criacao, ultimo_acesso, ativo, admin FROM usuarios ORDER BY nome");
        return $stmt->fetchAll();
    }
    
    /**
     * Cria um novo usuário
     */
    public function criar($nome, $email, $senha, $oab, $cargo, $telefone, $admin = false, $ativo = true) {
        $senha_hash = password_hash($senha, PASSWORD_DEFAULT);
        
        $stmt = $this->pdo->prepare("INSERT INTO usuarios (nome, email, senha_hash, oab, cargo, telefone, admin, ativo) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([$nome, $email, $senha_hash, $oab, $cargo, $telefone, $admin, $ativo]);
        
        return $this->pdo->lastInsertId();
    }
    
    /**
     * Atualiza um usuário existente
     */
    public function atualizar($id, $nome, $email, $oab, $cargo, $telefone, $admin, $ativo) {
        $stmt = $this->pdo->prepare("UPDATE usuarios SET nome = ?, email = ?, oab = ?, cargo = ?, telefone = ?, admin = ?, ativo = ? WHERE id = ?");
        return $stmt->execute([$nome, $email, $oab, $cargo, $telefone, $admin, $ativo, $id]);
    }
    
    /**
     * Atualiza a senha de um usuário
     */
    public function atualizarSenha($id, $senha) {
        $senha_hash = password_hash($senha, PASSWORD_DEFAULT);
        
        $stmt = $this->pdo->prepare("UPDATE usuarios SET senha_hash = ? WHERE id = ?");
        return $stmt->execute([$senha_hash, $id]);
    }
    
    /**
     * Atualiza o último acesso de um usuário
     */
    public function atualizarUltimoAcesso($id) {
        $stmt = $this->pdo->prepare("UPDATE usuarios SET ultimo_acesso = NOW() WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * Exclui um usuário
     */
    public function excluir($id) {
        $stmt = $this->pdo->prepare("DELETE FROM usuarios WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * Verifica se as credenciais são válidas
     */
    public function verificarCredenciais($email, $senha) {
        $usuario = $this->buscarPorEmail($email);
        
        if (!$usuario) {
            return false;
        }
        
        if (!$usuario['ativo']) {
            return false;
        }
        
        if (password_verify($senha, $usuario['senha_hash'])) {
            // Atualizar último acesso
            $this->atualizarUltimoAcesso($usuario['id']);
            return $usuario;
        }
        
        return false;
    }
    
    /**
     * Gera um token para redefinição de senha
     */
    public function gerarTokenRedefinicaoSenha($email) {
        $usuario = $this->buscarPorEmail($email);
        
        if (!$usuario) {
            return false;
        }
        
        // Gerar token aleatório
        $token = bin2hex(random_bytes(32));
        
        // Definir expiração para 24 horas
        $expiracao = date('Y-m-d H:i:s', strtotime('+24 hours'));
        
        // Salvar token no banco de dados
        $stmt = $this->pdo->prepare("UPDATE usuarios SET token_redefinicao = ?, expiracao_token = ? WHERE id = ?");
        $stmt->execute([$token, $expiracao, $usuario['id']]);
        
        return [
            'id' => $usuario['id'],
            'token' => $token,
            'email' => $usuario['email'],
            'nome' => $usuario['nome']
        ];
    }
    
    /**
     * Verifica se um token de redefinição de senha é válido
     */
    public function verificarTokenRedefinicaoSenha($id, $token) {
        $stmt = $this->pdo->prepare("SELECT id, token_redefinicao, expiracao_token FROM usuarios WHERE id = ? AND token_redefinicao = ? AND expiracao_token > NOW()");
        $stmt->execute([$id, $token]);
        
        return $stmt->fetch() ? true : false;
    }
    
    /**
     * Redefine a senha usando um token
     */
    public function redefinirSenhaComToken($id, $token, $novaSenha) {
        if (!$this->verificarTokenRedefinicaoSenha($id, $token)) {
            return false;
        }
        
        $senha_hash = password_hash($novaSenha, PASSWORD_DEFAULT);
        
        $stmt = $this->pdo->prepare("UPDATE usuarios SET senha_hash = ?, token_redefinicao = NULL, expiracao_token = NULL WHERE id = ?");
        return $stmt->execute([$senha_hash, $id]);
    }
}
