import os
import sys
import webbrowser
import threading
import time
from app import create_app, db
from app.models.usuario import <PERSON><PERSON><PERSON>

def open_browser():
    """Abre o navegador após um pequeno atraso"""
    time.sleep(2)
    webbrowser.open('http://localhost:5000')

def create_admin_if_not_exists():
    """Cria um usuário administrador se não existir nenhum"""
    with app.app_context():
        # Verificar se já existem usuários
        if Usuario.query.count() == 0:
            print("Criando usuário administrador...")
            
            # Criar usuário administrador
            admin = Usuario(
                nome="Administrador",
                email="<EMAIL>",
                oab="123456",
                cargo="Administrador",
                telefone="(11) 99999-9999",
                admin=True,
                ativo=True
            )
            admin.senha = "admin123"
            
            db.session.add(admin)
            db.session.commit()
            
            print("Usuário administrador criado com sucesso!")
            print("Email: <EMAIL>")
            print("Senha: admin123")

if __name__ == '__main__':
    # Criar a aplicação
    app = create_app('development')
    
    # Criar as tabelas do banco de dados se não existirem
    with app.app_context():
        db.create_all()
    
    # Criar usuário administrador se não existir
    create_admin_if_not_exists()
    
    # Iniciar thread para abrir o navegador
    threading.Timer(1.5, open_browser).start()
    
    # Iniciar o servidor
    print("Iniciando o servidor...")
    print("Acesse http://localhost:5000 no seu navegador")
    app.run(debug=False, host='0.0.0.0', port=5000)
