/* Estilos globais para o CRM Advocacia */

/* Cores do tema */
:root {
    --primary-color: #0A3161;    /* Azul marinho */
    --secondary-color: #D4AF37;  /* Dourado */
    --accent-color: #4A4A4A;     /* Cinza escuro */
    --text-color: #333333;       /* Quase preto */
    --bg-color: #F5F5F5;         /* Cinza claro */
    --light-color: #FFFFFF;      /* Branco */
    --danger-color: #DC3545;     /* Vermelho */
    --success-color: #28A745;    /* Verde */
    --warning-color: #FFC107;    /* <PERSON><PERSON> */
    --info-color: #17A2B8;       /* Azul claro */
}

/* Vídeo de fundo */
.video-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.video-background video {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -2; /* Garantir que o vídeo fique atrás da sobreposição */
}

.video-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8); /* Escurecido para melhorar o contraste */
    z-index: -1;
}

/* Estilos gerais */
body {
    font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
    color: var(--light-color);
    background-color: transparent;
    position: relative;
}

/* Fundo estático para páginas autenticadas */
body.authenticated {
    background: url('../img/LOGO PRETA.png') no-repeat center center fixed;
    background-size: contain;
    background-color: #f5f5f5;
}

/* Navbar */
.navbar-dark.bg-primary {
    background-color: rgba(10, 49, 97, 0.8) !important;
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 0.5px;
}

/* Cards */
.card {
    background-color: rgba(255, 255, 255, 0.9);
    color: var(--text-color);
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    backdrop-filter: blur(5px);
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
    font-weight: 600;
}

/* Botões */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #082a54;
    border-color: #082a54;
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    color: var(--text-color);
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: #c09c30;
    border-color: #c09c30;
    color: var(--text-color);
}

/* Links */
a {
    color: var(--primary-color);
    text-decoration: none;
}

a:hover {
    color: #082a54;
    text-decoration: underline;
}

/* Tabelas */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background-color: #0A3161;
    color: white;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table tbody td {
    color: #000;
    font-weight: 500;
}

.table-hover tbody tr:hover {
    background-color: rgba(10, 49, 97, 0.15);
}

/* Formulários */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(10, 49, 97, 0.25);
}

.form-label {
    font-weight: 500;
}

/* Badges */
.badge {
    padding: 0.4em 0.6em;
    font-weight: 500;
}

/* Footer */
.footer {
    background-color: rgba(10, 49, 97, 0.8) !important;
    color: var(--light-color);
    backdrop-filter: blur(10px);
}

.footer .text-muted {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Toast personalizado */
.toast {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    opacity: 1 !important;
    border-radius: 10px;
    overflow: hidden;
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.toast-header {
    border-bottom: none;
    padding: 0.75rem 1rem;
}

.toast-header .btn-close {
    margin-right: -0.375rem;
}

.toast-body {
    font-size: 1.1rem;
    padding: 0.75rem 1rem;
    font-weight: 500;
}
