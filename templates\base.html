<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CRM Advocacia{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <style>
        /* Cores do tema para escritório de advocacia */
        :root {
            --primary-color: #0A3161;    /* Azul marinho */
            --secondary-color: #D4AF37;  /* Dourado */
            --accent-color: #4A4A4A;     /* Cinza escuro */
            --text-color: #333333;       /* Quase preto */
            --bg-color: #F5F5F5;         /* Cinza claro */
            --light-color: #FFFFFF;      /* Branco */
            --danger-color: #DC3545;     /* Vermelho */
            --success-color: #28A745;    /* Verde */
            --warning-color: #FFC107;    /* Amarelo */
            --info-color: #17A2B8;       /* Azul claro */
        }

        /* Vídeo de fundo */
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .video-background video {
            position: absolute;
            top: 50%;
            left: 50%;
            min-width: 100%;
            min-height: 100%;
            width: auto;
            height: auto;
            transform: translateX(-50%) translateY(-50%);
            object-fit: cover;
        }

        .video-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: -1;
        }

        body {
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
            color: var(--light-color);
            background-color: transparent;
            position: relative;
        }

        .card {
            background-color: rgba(255, 255, 255, 0.9);
            color: var(--text-color);
        }

        /* Navbar personalizada */
        .navbar-dark.bg-primary {
            background-color: rgba(10, 49, 97, 0.8) !important;
            backdrop-filter: blur(10px);
        }

        .navbar-brand {
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            border-radius: 8px 8px 0 0 !important;
            font-weight: 600;
        }

        /* Botões */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: #082a54;
            border-color: #082a54;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: var(--text-color);
        }

        .btn-secondary:hover, .btn-secondary:focus {
            background-color: #c09c30;
            border-color: #c09c30;
            color: var(--text-color);
        }

        /* Links */
        a {
            color: var(--primary-color);
            text-decoration: none;
        }

        a:hover {
            color: #082a54;
            text-decoration: underline;
        }

        /* Tabelas */
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: 600;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(10, 49, 97, 0.05);
        }

        /* Formulários */
        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(10, 49, 97, 0.25);
        }

        .form-label {
            font-weight: 500;
        }

        /* Badges */
        .badge {
            padding: 0.4em 0.6em;
            font-weight: 500;
        }

        /* Footer */
        .footer {
            background-color: rgba(10, 49, 97, 0.8) !important;
            color: var(--light-color);
            backdrop-filter: blur(10px);
        }

        .footer .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        /* Toast personalizado */
        .toast {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            opacity: 1 !important;
            border-radius: 10px;
            overflow: hidden;
            animation: slideIn 0.3s ease-in-out;
        }

        @keyframes slideIn {
            from {
                transform: translateY(100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .toast-header {
            border-bottom: none;
            padding: 0.75rem 1rem;
        }

        .toast-header .btn-close {
            margin-right: -0.375rem;
        }

        .toast-body {
            font-size: 1.1rem;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }
    </style>

    {% block styles %}{% endblock %}
</head>
<body{% if current_user.is_authenticated %} class="authenticated"{% endif %}>
    {% block background %}{% endblock %}

    {% if current_user.is_authenticated %}
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <img src="{{ url_for('static', filename='img/LOGO BRANCA.png') }}" alt="CRM Advocacia" height="40">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}" style="font-weight: bold; color: {% if request.endpoint == 'dashboard' %}white{% else %}#D4AF37{% endif %};">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-gavel me-1"></i>Processos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-users me-1"></i>Clientes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-file-alt me-1"></i>Documentos
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <!-- Botão de Administração - Visível apenas para administradores -->
                    {% if current_user.admin %}
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link dropdown-toggle {% if request.endpoint == 'listar_usuarios' or request.endpoint == 'novo_usuario' %}active{% endif %}" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>Administração
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('novo_usuario') }}">
                                    <i class="fas fa-user-plus me-1"></i>Novo Usuário
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('listar_usuarios') }}">
                                    <i class="fas fa-users-cog me-1"></i>Gerenciar Usuários
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-database me-1"></i>Configurações do Sistema
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% endif %}

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>{{ current_user.nome }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-id-card me-1"></i>Perfil
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{{ url_for('logout') }}">
                                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Flash Messages -->
    <div class="container mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="container mt-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <p class="text-muted mb-1">Sistema de Gerenciamento de Escritório de Advocacia</p>
            <p class="text-muted mb-1">Projeto desenvolvido por José Ricardo Toledo</p>
            <p class="text-muted mb-0">© 2025 CRM Advocacia. Todos os direitos reservados.</p>
        </div>
    </footer>

    <!-- Toast de Boas-vindas -->
    {% if mostrar_boas_vindas %}
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
        <div id="welcomeToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header" style="background-color: #0A3161; color: white;">
                <i class="fas fa-user-check me-2"></i>
                <strong class="me-auto">Bem-vindo!</strong>
                <small style="color: rgba(255, 255, 255, 0.8);">Agora</small>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Fechar"></button>
            </div>
            <div class="toast-body" style="background-color: #D4AF37; color: #000; font-weight: bold;">
                Bem-vindo, {{ current_user.nome }}!
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom JS -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializar tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Inicializar popovers
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });

            // Mostrar toast de boas-vindas
            var welcomeToastEl = document.getElementById('welcomeToast');
            if (welcomeToastEl) {
                var welcomeToast = new bootstrap.Toast(welcomeToastEl, {
                    delay: 5000
                });
                welcomeToast.show();
            }

            // Garantir que o vídeo de fundo seja carregado e reproduzido corretamente
            var video = document.getElementById('background-video');
            if (video) {
                // Tentar carregar o vídeo novamente se falhar
                video.addEventListener('error', function() {
                    console.log('Erro ao carregar o vídeo. Tentando novamente...');
                    var currentSrc = video.querySelector('source').src;
                    video.querySelector('source').src = currentSrc + '?t=' + new Date().getTime();
                    video.load();
                });

                // Forçar o play do vídeo
                video.play().catch(function(error) {
                    console.log('Erro ao reproduzir o vídeo:', error);
                });
            }
        });
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
