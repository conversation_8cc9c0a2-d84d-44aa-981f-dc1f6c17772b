{% extends 'base.html' %}

{% block title %}Dashboard - CRM Advocacia{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 style="color: #D4AF37; text-shadow: 1px 1px 3px rgba(0,0,0,0.7); font-weight: bold;"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</h2>
        <p style="color: white; font-weight: 500; text-shadow: 1px 1px 3px rgba(0,0,0,0.5);">Be<PERSON>-vindo, {{ current_user.nome }}. Aqui está um resumo das suas atividades.</p>
    </div>
</div>

<!-- Cards de Estatísticas -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Processos</h5>
                        <h2 class="mb-0">{{ total_processos }}</h2>
                    </div>
                    <i class="fas fa-gavel fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <span>Ver todos</span>
                <a href="#" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title">Clientes</h5>
                        <h2 class="mb-0">{{ total_clientes }}</h2>
                    </div>
                    <i class="fas fa-users fa-3x opacity-50"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center">
                <span>Ver todos</span>
                <a href="#" class="text-white">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title" style="color: #000; font-weight: bold;">Prazos Próximos</h5>
                        <h2 class="mb-0" style="color: #000; font-weight: bold;">{{ prazos_proximos|length }}</h2>
                    </div>
                    <i class="fas fa-calendar-alt fa-3x" style="color: #000; opacity: 0.7;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between align-items-center" style="background-color: #e0a800; color: #000; font-weight: bold;">
                <span>Ver todos</span>
                <a href="#" style="color: #000;">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Processos por Status -->
    <div class="col-md-6 mb-4">
        <div class="card shadow">
            <div class="card-header" style="background-color: #0A3161; color: white;">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Processos por Status</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Status</th>
                                <th>Quantidade</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for status, count in processos_por_status %}
                            <tr>
                                <td>{{ status }}</td>
                                <td>{{ count }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="2" class="text-center">Nenhum processo encontrado</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Prazos Próximos -->
    <div class="col-md-6 mb-4" id="prazos-proximos">
        <div class="card shadow">
            <div class="card-header" style="background-color: #0A3161; color: white;">
                <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>Prazos Próximos</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Prazo</th>
                                <th>Processo</th>
                                <th>Data Limite</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for prazo in prazos_proximos %}
                            <tr>
                                <td>{{ prazo.titulo }}</td>
                                <td>{{ prazo.processo.numero }}</td>
                                <td>{{ prazo.data_fim.strftime('%d/%m/%Y') }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center">Nenhum prazo próximo</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Processos Recentes -->
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header" style="background-color: #0A3161; color: white;">
                <h5 class="mb-0"><i class="fas fa-gavel me-2"></i>Processos Recentes</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Número</th>
                                <th>Título</th>
                                <th>Cliente</th>
                                <th>Status</th>
                                <th>Data Cadastro</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for processo in processos_recentes %}
                            <tr>
                                <td>{{ processo.numero }}</td>
                                <td>{{ processo.titulo }}</td>
                                <td>{{ processo.cliente.nome }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ processo.status }}</span>
                                </td>
                                <td>{{ processo.data_cadastro.strftime('%d/%m/%Y') }}</td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center">Nenhum processo encontrado</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer text-end">
                <a href="#" class="btn btn-primary btn-sm">
                    Ver Todos <i class="fas fa-arrow-right ms-1"></i>
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
