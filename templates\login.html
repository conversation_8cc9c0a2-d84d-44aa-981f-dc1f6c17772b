{% extends 'base.html' %}

{% block title %}Login - CRM Advocacia{% endblock %}

{% block styles %}
<style>
    .login-container {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 80vh;
    }

    .login-card {
        width: 100%;
        max-width: 450px;
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        background-color: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
    }

    .login-header {
        background-color: var(--primary-color);
        color: white;
        border-radius: 10px 10px 0 0;
        padding: 30px;
        text-align: center;
    }

    .login-logo {
        margin: 0;
    }

    .login-title {
        font-size: 1.8rem;
        font-weight: 600;
        margin: 0;
    }

    .login-subtitle {
        font-size: 1rem;
        opacity: 0.8;
        margin-top: 5px;
    }

    .login-body {
        padding: 30px;
    }

    .login-footer {
        text-align: center;
        padding: 15px;
        border-top: 1px solid #eee;
    }
</style>
{% endblock %}

{% block background %}
<!-- Vídeo de fundo -->
<div class="video-background">
    <video autoplay muted loop playsinline id="background-video">
        <source src="{{ url_for('static', filename='video/Videodefundo.mp4') }}" type="video/mp4">
        <!-- Fallback para navegadores que não suportam vídeo -->
        Seu navegador não suporta vídeos HTML5.
    </video>
    <div class="video-overlay"></div>
</div>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="card login-card">
        <div class="login-header">
            <div class="login-logo">
                <img src="{{ url_for('static', filename='img/LOGO BRANCA.png') }}" alt="CRM Advocacia" height="100">
            </div>
        </div>

        <div class="login-body">
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" action="{{ url_for('login') }}">
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                        <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="senha" class="form-label">Senha</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                        <input type="password" class="form-control" id="senha" name="senha" placeholder="Sua senha" required>
                        <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="lembrar_me" name="lembrar_me">
                    <label class="form-check-label" for="lembrar_me">Lembrar-me</label>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>Entrar
                    </button>
                </div>
            </form>
        </div>

        <div class="login-footer">
            <p class="mb-0">
                <a href="{{ url_for('index') }}">Voltar para a página inicial</a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Alternar visibilidade da senha
        const togglePassword = document.getElementById('toggle-password');
        const passwordInput = document.getElementById('senha');

        if (togglePassword && passwordInput) {
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });
        }

        // Garantir que o vídeo de fundo seja carregado e reproduzido corretamente
        var video = document.getElementById('background-video');
        if (video) {
            // Forçar o play do vídeo
            video.play().catch(function(error) {
                console.log('Erro ao reproduzir o vídeo:', error);
            });
        }
    });
</script>
{% endblock %}
