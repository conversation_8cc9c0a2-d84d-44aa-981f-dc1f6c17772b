<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redefinir <PERSON>ha - CRM Advocacia</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        /* Cores do tema para escritório de advocacia */
        :root {
            --primary-color: #0A3161;    /* Azul marinho */
            --secondary-color: #D4AF37;  /* Dourado */
            --accent-color: #4A4A4A;     /* Cinza escuro */
            --text-color: #333333;       /* Quase preto */
            --bg-color: #F5F5F5;         /* Cinza claro */
            --light-color: #FFFFFF;      /* Branco */
        }

        /* Vídeo de fundo */
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .video-background video {
            position: absolute;
            top: 50%;
            left: 50%;
            min-width: 100%;
            min-height: 100%;
            width: auto;
            height: auto;
            transform: translateX(-50%) translateY(-50%);
            object-fit: cover;
        }

        .video-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6);
            z-index: -1;
        }

        body {
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
            color: var(--light-color);
            background-color: transparent;
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .reset-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .reset-card {
            width: 100%;
            max-width: 450px;
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            background-color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }

        .reset-header {
            background-color: var(--primary-color);
            color: white;
            border-radius: 10px 10px 0 0;
            padding: 20px;
            text-align: center;
        }

        .reset-logo {
            margin-bottom: 15px;
        }

        .reset-logo i {
            font-size: 3rem;
            color: var(--secondary-color);
        }

        .reset-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin: 0;
        }

        .reset-subtitle {
            font-size: 1rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        .reset-body {
            padding: 30px;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(10, 49, 97, 0.25);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: #082a54;
            border-color: #082a54;
        }

        .reset-footer {
            text-align: center;
            padding: 15px;
            border-top: 1px solid #eee;
        }

        .footer {
            background-color: rgba(10, 49, 97, 0.8);
            color: white;
            text-align: center;
            padding: 15px 0;
            margin-top: auto;
            backdrop-filter: blur(10px);
        }

        .footer .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .alert {
            margin-bottom: 20px;
        }

        .password-strength {
            margin-top: 5px;
            height: 5px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .password-strength-text {
            font-size: 0.8rem;
            margin-top: 5px;
        }

        .strength-weak {
            background-color: #dc3545;
            width: 25%;
        }

        .strength-medium {
            background-color: #ffc107;
            width: 50%;
        }

        .strength-strong {
            background-color: #28a745;
            width: 75%;
        }

        .strength-very-strong {
            background-color: #198754;
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- Vídeo de fundo -->
    <div class="video-background">
        <video autoplay muted loop id="background-video">
            <source src="{{ url_for('static', filename='video/Videodefundo.mp4') }}" type="video/mp4">
            <!-- Fallback para navegadores que não suportam vídeo -->
            Seu navegador não suporta vídeos HTML5.
        </video>
        <div class="video-overlay"></div>
    </div>

    <div class="reset-container">
        <div class="card reset-card">
            <div class="reset-header">
                <div class="reset-logo">
                    <img src="{{ url_for('static', filename='img/LOGO BRANCA.png') }}" alt="CRM Advocacia" height="60">
                </div>
                <h1 class="reset-title">Redefinir Senha</h1>
                <p class="reset-subtitle">CRM Advocacia</p>
            </div>

            <div class="reset-body">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>Digite sua nova senha abaixo. Certifique-se de escolher uma senha forte e segura.
                </div>

                <form method="POST">
                    <input type="hidden" name="token" value="{{ token }}">

                    <div class="mb-3">
                        <label for="senha" class="form-label">Nova Senha</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="senha" name="senha" placeholder="Digite sua nova senha" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggle-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="password-strength" id="password-strength"></div>
                        <div class="password-strength-text" id="password-strength-text"></div>
                    </div>

                    <div class="mb-4">
                        <label for="confirmar_senha" class="form-label">Confirmar Nova Senha</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="confirmar_senha" name="confirmar_senha" placeholder="Confirme sua nova senha" required>
                            <button class="btn btn-outline-secondary" type="button" id="toggle-confirm-password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>Redefinir Senha
                        </button>
                    </div>
                </form>
            </div>

            <div class="reset-footer">
                <p class="mb-0">Lembrou sua senha? <a href="{{ url_for('login') }}">Voltar para o login</a></p>
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <span class="text-muted">© 2023 CRM Advocacia. Todos os direitos reservados.</span>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Alternar visibilidade da senha
            const togglePassword = document.getElementById('toggle-password');
            const passwordInput = document.getElementById('senha');

            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });

            // Alternar visibilidade da confirmação de senha
            const toggleConfirmPassword = document.getElementById('toggle-confirm-password');
            const confirmPasswordInput = document.getElementById('confirmar_senha');

            toggleConfirmPassword.addEventListener('click', function() {
                const type = confirmPasswordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                confirmPasswordInput.setAttribute('type', type);
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });

            // Verificar força da senha
            const passwordStrength = document.getElementById('password-strength');
            const passwordStrengthText = document.getElementById('password-strength-text');

            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = checkPasswordStrength(password);

                // Remover classes anteriores
                passwordStrength.className = 'password-strength';

                // Adicionar classe de acordo com a força
                if (password.length === 0) {
                    passwordStrength.style.width = '0';
                    passwordStrengthText.textContent = '';
                } else if (strength === 1) {
                    passwordStrength.classList.add('strength-weak');
                    passwordStrengthText.textContent = 'Fraca';
                    passwordStrengthText.style.color = '#dc3545';
                } else if (strength === 2) {
                    passwordStrength.classList.add('strength-medium');
                    passwordStrengthText.textContent = 'Média';
                    passwordStrengthText.style.color = '#ffc107';
                } else if (strength === 3) {
                    passwordStrength.classList.add('strength-strong');
                    passwordStrengthText.textContent = 'Forte';
                    passwordStrengthText.style.color = '#28a745';
                } else {
                    passwordStrength.classList.add('strength-very-strong');
                    passwordStrengthText.textContent = 'Muito Forte';
                    passwordStrengthText.style.color = '#198754';
                }
            });

            /**
             * Verifica a força da senha
             * @param {string} password - Senha a ser verificada
             * @returns {number} - Nível de força (1-4)
             */
            function checkPasswordStrength(password) {
                let strength = 0;

                // Comprimento mínimo
                if (password.length >= 8) {
                    strength += 1;
                }

                // Letras maiúsculas e minúsculas
                if (password.match(/([a-z].*[A-Z])|([A-Z].*[a-z])/)) {
                    strength += 1;
                }

                // Números e caracteres
                if (password.match(/([a-zA-Z])/) && password.match(/([0-9])/)) {
                    strength += 1;
                }

                // Caracteres especiais
                if (password.match(/([!,%,&,@,#,$,^,*,?,_,~])/)) {
                    strength += 1;
                }

                return Math.min(strength, 4);
            }
        });
    </script>
</body>
</html>
