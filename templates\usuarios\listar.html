{% extends 'base.html' %}

{% block title %}Gerenciar Usuários - CRM Advocacia{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2><i class="fas fa-users-cog me-2"></i>Gerenciar Usuários</h2>
        <p class="text-muted">Gerencie os usuários do sistema, edite informações e redefina senhas.</p>
    </div>
</div>

<!-- Lista de Usuários -->
<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>Lista de Usuários</h5>
                <a href="{{ url_for('novo_usuario') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-user-plus me-1"></i>Novo Usuário
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Nome</th>
                                <th>Email</th>
                                <th>OAB</th>
                                <th>Cargo</th>
                                <th>Telefone</th>
                                <th>Tipo</th>
                                <th>Status</th>
                                <th>Último Acesso</th>
                                <th>Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for usuario in usuarios %}
                            <tr>
                                <td>{{ usuario.nome }}</td>
                                <td>{{ usuario.email }}</td>
                                <td>{{ usuario.oab }}</td>
                                <td>{{ usuario.cargo }}</td>
                                <td>{{ usuario.telefone }}</td>
                                <td>{{ 'Administrador' if usuario.admin else 'Advogado' }}</td>
                                <td>
                                    {% if usuario.ativo %}
                                    <span class="text-success"><i class="fas fa-check-circle me-1"></i>Ativo</span>
                                    {% else %}
                                    <span class="text-danger"><i class="fas fa-times-circle me-1"></i>Inativo</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if usuario.ultimo_acesso %}
                                    {{ usuario.ultimo_acesso.strftime('%d/%m/%Y %H:%M') }}
                                    {% else %}
                                    Nunca
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="#" class="btn btn-primary" data-bs-toggle="tooltip" title="Editar">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-warning" data-bs-toggle="tooltip" title="Redefinir Senha">
                                            <i class="fas fa-key"></i>
                                        </a>
                                        {% if usuario.id != current_user.id %}
                                        <a href="#" class="btn btn-danger" data-bs-toggle="tooltip" title="Excluir">
                                            <i class="fas fa-trash-alt"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center">Nenhum usuário encontrado</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de Redefinir Senha -->
<div class="modal fade" id="redefinirSenhaModal" tabindex="-1" aria-labelledby="redefinirSenhaModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="redefinirSenhaModalLabel">
                    <i class="fas fa-key me-2"></i>Redefinir Senha
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fechar"></button>
            </div>
            <div class="modal-body">
                <p>Um link para redefinição de senha será enviado para o email do usuário.</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>O link de redefinição será válido por 24 horas.
                </div>
                <form id="redefinirSenhaForm">
                    <input type="hidden" id="redefinirId" name="redefinirId">
                    <div class="mb-3">
                        <label for="redefinirEmail" class="form-label">Email do Usuário</label>
                        <input type="email" class="form-control" id="redefinirEmail" name="redefinirEmail" readonly>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" id="btnEnviarRedefinicao">
                    <i class="fas fa-paper-plane me-1"></i>Enviar Link
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Inicializar DataTable
        const table = new DataTable('table', {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/pt-BR.json'
            }
        });
    });
</script>
{% endblock %}
