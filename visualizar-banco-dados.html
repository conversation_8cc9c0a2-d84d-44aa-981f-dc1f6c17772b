<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualização do Banco de Dados - CRM Advocacia</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    
    <style>
        /* Cores do tema para escritório de advocacia */
        :root {
            --primary-color: #0A3161;    /* Azul marinho */
            --secondary-color: #D4AF37;  /* Dourado */
            --accent-color: #4A4A4A;     /* Cinza escuro */
            --text-color: #333333;       /* Quase preto */
            --bg-color: #F5F5F5;         /* Cinza claro */
            --light-color: #FFFFFF;      /* Branco */
        }

        body {
            font-family: 'Roboto', 'Segoe UI', Arial, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
        }

        /* Navbar personalizada */
        .navbar-dark.bg-primary {
            background-color: var(--primary-color) !important;
        }

        .navbar-brand {
            font-weight: 700;
            letter-spacing: 0.5px;
        }

        /* Cards */
        .card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card-header {
            border-radius: 8px 8px 0 0 !important;
            font-weight: 600;
        }

        /* Botões */
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: #082a54;
            border-color: #082a54;
        }

        /* Footer */
        .footer {
            background-color: var(--primary-color) !important;
            color: var(--light-color);
            padding: 1rem 0;
            margin-top: 2rem;
        }

        .footer .text-muted {
            color: rgba(255, 255, 255, 0.8) !important;
        }
        
        /* Tabelas */
        .table-container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .table-title {
            color: var(--primary-color);
            margin-bottom: 20px;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 10px;
        }
        
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            overflow-x: auto;
        }
        
        .nav-tabs .nav-link {
            color: var(--primary-color);
        }
        
        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            font-weight: bold;
            border-bottom: 3px solid var(--secondary-color);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="demo.html">
                <i class="fas fa-balance-scale me-2"></i>CRM Advocacia
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="demo.html">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-gavel me-1"></i>Processos
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-users me-1"></i>Clientes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">
                            <i class="fas fa-file-alt me-1"></i>Documentos
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <!-- Botão de Administração -->
                    <li class="nav-item dropdown me-2">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>Administração
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#novoUsuarioModal">
                                    <i class="fas fa-user-plus me-1"></i>Novo Usuário
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="gerenciar-usuarios.html">
                                    <i class="fas fa-users-cog me-1"></i>Gerenciar Usuários
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item active" href="visualizar-banco-dados.html">
                                    <i class="fas fa-database me-1"></i>Banco de Dados
                                </a>
                            </li>
                        </ul>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>Administrador
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="#">
                                    <i class="fas fa-id-card me-1"></i>Perfil
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="login.html">
                                    <i class="fas fa-sign-out-alt me-1"></i>Sair
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="container mt-4">
        <div class="row mb-4">
            <div class="col-md-12">
                <h2><i class="fas fa-database me-2"></i>Visualização do Banco de Dados</h2>
                <p class="text-muted">Visualize os dados armazenados no sistema (simulação).</p>
            </div>
        </div>

        <!-- Tabs de Navegação -->
        <ul class="nav nav-tabs mb-4" id="dbTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="usuarios-tab" data-bs-toggle="tab" data-bs-target="#usuarios" type="button" role="tab" aria-controls="usuarios" aria-selected="true">
                    <i class="fas fa-users me-1"></i>Usuários
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="clientes-tab" data-bs-toggle="tab" data-bs-target="#clientes" type="button" role="tab" aria-controls="clientes" aria-selected="false">
                    <i class="fas fa-user-tie me-1"></i>Clientes
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="processos-tab" data-bs-toggle="tab" data-bs-target="#processos" type="button" role="tab" aria-controls="processos" aria-selected="false">
                    <i class="fas fa-gavel me-1"></i>Processos
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="documentos-tab" data-bs-toggle="tab" data-bs-target="#documentos" type="button" role="tab" aria-controls="documentos" aria-selected="false">
                    <i class="fas fa-file-alt me-1"></i>Documentos
                </button>
            </li>
        </ul>
        
        <!-- Conteúdo das Tabs -->
        <div class="tab-content" id="dbTabsContent">
            <!-- Tab Usuários -->
            <div class="tab-pane fade show active" id="usuarios" role="tabpanel" aria-labelledby="usuarios-tab">
                <div class="table-container">
                    <h3 class="table-title"><i class="fas fa-users me-2"></i>Tabela de Usuários</h3>
                    <div class="table-responsive">
                        <table id="tabelaUsuarios" class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nome</th>
                                    <th>Email</th>
                                    <th>OAB</th>
                                    <th>Cargo</th>
                                    <th>Telefone</th>
                                    <th>Admin</th>
                                    <th>Ativo</th>
                                    <th>Último Acesso</th>
                                </tr>
                            </thead>
                            <tbody id="usuariosTableBody">
                                <!-- Preenchido via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                    
                    <h4 class="mt-4">Dados Brutos:</h4>
                    <pre id="usuariosRaw"><!-- Preenchido via JavaScript --></pre>
                </div>
            </div>
            
            <!-- Tab Clientes -->
            <div class="tab-pane fade" id="clientes" role="tabpanel" aria-labelledby="clientes-tab">
                <div class="table-container">
                    <h3 class="table-title"><i class="fas fa-user-tie me-2"></i>Tabela de Clientes</h3>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Dados de demonstração - Em um sistema real, estes dados seriam armazenados em um banco de dados.
                    </div>
                    
                    <pre>
[
  {
    "id": 1,
    "nome": "Empresa ABC Ltda",
    "cpf_cnpj": "12.345.678/0001-90",
    "tipo": "PJ",
    "email": "<EMAIL>",
    "telefone": "(11) 3333-4444",
    "celular": "(11) 99999-8888",
    "endereco": "Av. Paulista, 1000",
    "cidade": "São Paulo",
    "estado": "SP",
    "advogado_id": 1
  },
  {
    "id": 2,
    "nome": "Maria Silva",
    "cpf_cnpj": "123.456.789-00",
    "tipo": "PF",
    "email": "<EMAIL>",
    "celular": "(11) 97777-6666",
    "endereco": "Rua das Flores, 123",
    "cidade": "São Paulo",
    "estado": "SP",
    "advogado_id": 2
  },
  {
    "id": 3,
    "nome": "João Pereira",
    "cpf_cnpj": "987.654.321-00",
    "tipo": "PF",
    "email": "<EMAIL>",
    "celular": "(11) 96666-5555",
    "endereco": "Av. Brasil, 500",
    "cidade": "Rio de Janeiro",
    "estado": "RJ",
    "advogado_id": 2
  }
]
                    </pre>
                </div>
            </div>
            
            <!-- Tab Processos -->
            <div class="tab-pane fade" id="processos" role="tabpanel" aria-labelledby="processos-tab">
                <div class="table-container">
                    <h3 class="table-title"><i class="fas fa-gavel me-2"></i>Tabela de Processos</h3>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Dados de demonstração - Em um sistema real, estes dados seriam armazenados em um banco de dados.
                    </div>
                    
                    <pre>
[
  {
    "id": 1,
    "numero": "1234567-12.2023.8.26.0100",
    "titulo": "Ação de Cobrança",
    "tipo": "Civil",
    "area": "Contratos",
    "vara": "5ª Vara Cível",
    "comarca": "São Paulo",
    "tribunal": "TJSP",
    "valor_causa": 50000.00,
    "status": "Em andamento",
    "data_inicio": "2023-01-15",
    "cliente_id": 1,
    "advogado_id": 1
  },
  {
    "id": 2,
    "numero": "9876543-98.2023.8.26.0100",
    "titulo": "Ação Trabalhista",
    "tipo": "Trabalhista",
    "area": "Direito do Trabalho",
    "vara": "10ª Vara do Trabalho",
    "comarca": "São Paulo",
    "tribunal": "TRT-2",
    "valor_causa": 30000.00,
    "status": "Em andamento",
    "data_inicio": "2023-02-20",
    "cliente_id": 2,
    "advogado_id": 2
  },
  {
    "id": 3,
    "numero": "5555555-55.2023.8.26.0100",
    "titulo": "Ação de Indenização",
    "tipo": "Civil",
    "area": "Responsabilidade Civil",
    "vara": "3ª Vara Cível",
    "comarca": "São Paulo",
    "tribunal": "TJSP",
    "valor_causa": 75000.00,
    "status": "Concluído",
    "data_inicio": "2023-03-10",
    "cliente_id": 3,
    "advogado_id": 2
  }
]
                    </pre>
                </div>
            </div>
            
            <!-- Tab Documentos -->
            <div class="tab-pane fade" id="documentos" role="tabpanel" aria-labelledby="documentos-tab">
                <div class="table-container">
                    <h3 class="table-title"><i class="fas fa-file-alt me-2"></i>Tabela de Documentos</h3>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Dados de demonstração - Em um sistema real, estes dados seriam armazenados em um banco de dados.
                    </div>
                    
                    <pre>
[
  {
    "id": 1,
    "nome": "Petição Inicial",
    "tipo": "Petição",
    "formato": "PDF",
    "tamanho": 1024000,
    "data_upload": "2023-01-20",
    "processo_id": 1
  },
  {
    "id": 2,
    "nome": "Procuração",
    "tipo": "Procuração",
    "formato": "PDF",
    "tamanho": 512000,
    "data_upload": "2023-01-20",
    "processo_id": 1
  },
  {
    "id": 3,
    "nome": "Contrato Social",
    "tipo": "Documento",
    "formato": "PDF",
    "tamanho": 2048000,
    "data_upload": "2023-01-20",
    "cliente_id": 1
  }
]
                    </pre>
                </div>
            </div>
        </div>
    </main>
    
    <!-- Footer -->
    <footer class="footer py-3">
        <div class="container text-center">
            <span class="text-muted">© 2023 CRM Advocacia. Todos os direitos reservados.</span>
        </div>
    </footer>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Script de autenticação -->
    <script src="app/static/js/auth.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar se o usuário está logado
            if (!estaLogado()) {
                window.location.href = 'login.html';
                return;
            }
            
            // Verificar se o usuário é administrador
            const usuario = obterUsuarioLogado();
            if (!usuario || !usuario.admin) {
                alert('Você não tem permissão para acessar esta página.');
                window.location.href = 'demo.html';
                return;
            }
            
            // Atualizar nome do usuário na barra de navegação
            const userDropdown = document.getElementById('userDropdown');
            if (userDropdown && usuario) {
                userDropdown.innerHTML = `<i class="fas fa-user-circle me-1"></i>${usuario.nome}`;
            }
            
            // Configurar botão de logout
            const logoutButton = document.querySelector('a[href="login.html"]');
            if (logoutButton) {
                logoutButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    realizarLogout();
                });
            }
            
            // Preencher tabela de usuários
            const usuariosTableBody = document.getElementById('usuariosTableBody');
            const usuariosRaw = document.getElementById('usuariosRaw');
            
            if (usuariosTableBody && usuariosRaw) {
                // Obter usuários
                const usuarios = obterUsuarios();
                
                // Preencher tabela
                usuarios.forEach(usuario => {
                    const { senha, ...usuarioSemSenha } = usuario;
                    const row = document.createElement('tr');
                    
                    row.innerHTML = `
                        <td>${usuarioSemSenha.id}</td>
                        <td>${usuarioSemSenha.nome}</td>
                        <td>${usuarioSemSenha.email}</td>
                        <td>${usuarioSemSenha.oab}</td>
                        <td>${usuarioSemSenha.cargo || '-'}</td>
                        <td>${usuarioSemSenha.telefone || '-'}</td>
                        <td>${usuarioSemSenha.admin ? 'Sim' : 'Não'}</td>
                        <td>${usuarioSemSenha.ativo ? 'Sim' : 'Não'}</td>
                        <td>${usuarioSemSenha.ultimo_acesso ? new Date(usuarioSemSenha.ultimo_acesso).toLocaleString('pt-BR') : 'Nunca'}</td>
                    `;
                    
                    usuariosTableBody.appendChild(row);
                });
                
                // Preencher dados brutos
                const usuariosSemSenha = usuarios.map(usuario => {
                    const { senha, ...usuarioSemSenha } = usuario;
                    return usuarioSemSenha;
                });
                
                usuariosRaw.textContent = JSON.stringify(usuariosSemSenha, null, 2);
                
                // Inicializar DataTable
                $('#tabelaUsuarios').DataTable({
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/pt-BR.json'
                    },
                    responsive: true
                });
            }
        });
    </script>
</body>
</html>
